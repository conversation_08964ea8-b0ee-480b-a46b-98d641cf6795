import { useSelector } from 'react-redux';
import { RootState } from '../Redux/store';

const NurseDashboard = () => {
  const nurse = useSelector((state: RootState) => state.nurse.nurse);

  if (!nurse)
    return (
      <p className='text-center text-3xl text-red-600 font-black  m-4'>
        No nurse data available.
      </p>
    );
  return (
    <div className='p-4 bg-gray-100 rounded-lg'>
      <h2 className='text-lg font-bold'>Welcome, {nurse.name}</h2>
      <p>Phone: {nurse.phoneNumber}</p>
      <p>Specialization: {nurse.specialization}</p>
      <p>Experience: {nurse.experience} years</p>
    </div>
  );
};

export default NurseDashboard;
