import React, { useState, useCallback } from 'react';
import { useUploadDocumentMutation } from '@/store/api/apiSlice';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/utils/toast';
import { Upload, Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { NullableFile, NullableString } from '@/types/common';

interface DocumentUploadFormProps {
  onUploadComplete?: (url: string) => void;
}

const DocumentUploadForm: React.FC<DocumentUploadFormProps> = ({
  onUploadComplete,
}) => {
  const [uploadDocument, { isLoading }] = useUploadDocumentMutation();
  const [selectedFile, setSelectedFile] = useState<NullableFile>(null);
  const [previewUrl, setPreviewUrl] = useState<NullableString>(null);

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        setSelectedFile(file);

        if (file.type.startsWith('image/')) {
          const url = URL.createObjectURL(file);
          setPreviewUrl(url);
        } else {
          setPreviewUrl(null);
        }
      }
    },
    []
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedFile) {
      toast.error('Please select a file to upload');
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const result = await uploadDocument(formData).unwrap();

      if (result.success && result.url) {
        toast.success('Document uploaded successfully');
        if (onUploadComplete) {
          onUploadComplete(result.url);
        }

        setSelectedFile(null);
        setPreviewUrl(null);
      } else {
        toast.error(result.message || 'Failed to upload document');
      }
    } catch (error) {
      toast.error('Failed to upload document. Please try again.');
      console.error('Upload error:', error);
    }
  };

  return (
    <Card className='p-6'>
      <form onSubmit={handleSubmit} className='space-y-4'>
        <div className='space-y-2'>
          <label className='text-sm font-medium'>Upload Document</label>
          <div className='flex items-center gap-4'>
            <Input
              type='file'
              onChange={handleFileChange}
              accept='image/*,.pdf,.doc,.docx'
              className='flex-1'
            />
            <Button
              type='submit'
              disabled={!selectedFile || isLoading}
              className='flex items-center gap-2'
            >
              {isLoading ? (
                <>
                  <Loader2 className='h-4 w-4 animate-spin' />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className='h-4 w-4' />
                  Upload
                </>
              )}
            </Button>
          </div>
        </div>

        {previewUrl && (
          <div className='mt-4'>
            <img
              src={previewUrl}
              alt='Preview'
              className='max-w-full h-auto rounded-md'
            />
          </div>
        )}

        {selectedFile && !previewUrl && (
          <div className='mt-4 text-sm text-muted-foreground'>
            Selected file: {selectedFile.name}
          </div>
        )}
      </form>
    </Card>
  );
};

export default DocumentUploadForm;
