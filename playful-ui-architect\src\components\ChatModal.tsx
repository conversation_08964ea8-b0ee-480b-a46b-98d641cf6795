import React from 'react';
import { ChatInterface, ChatState } from './ChatInterface';
import { Chat<PERSON><PERSON><PERSON> } from './ChatRenderer';

interface ChatModalProps {
  selectedPatient?: {
    customer_cognitoId: string;
    customer_given_name: string;
  } | null;
  isOpen?: boolean;
  onClose?: () => void;

  mode?: 'modal' | 'fullscreen';
}

const ChatModal: React.FC<ChatModalProps> = ({
  selectedPatient,
  isOpen = true,
  onClose,
  mode = 'modal',
}) => {
  return (
    <ChatInterface
      selectedPatient={selectedPatient}
      isOpen={isOpen}
      onClose={onClose}
      mode={mode}
    >
      {(chatState: ChatState) => (
        <ChatRenderer chatState={chatState} mode={mode} isOpen={isOpen} />
      )}
    </ChatInterface>
  );
};

export default ChatModal;
