import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/utils/toast';
import { useRegisterNurseMutation } from '@/store/api/apiSlice';
import type { NurseRegistrationData } from '@/types/auth';
import { Eye, EyeOff } from 'lucide-react';
import Logo from '../components/onBoarding/Logo/Logo';

const signUpSchema = z
  .object({
    given_name: z.string().min(2, 'First name must be at least 4 characters'),
    middle_name: z.string().optional(),
    family_name: z.string().min(1, 'Last name must be at least 1 character'),
    email: z.string().email('Enter a valid email address'),
    phone_number: z
      .string()
      .regex(/^\+91\d{10}$/, 'Enter a valid 10-digit number'),
    password: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .regex(
        /[@#$!%*?&-_+<>?/]/,
        'Password must contain at least one special character (@$!%*?&-_+<>/)'
      )
      .regex(
        /[A-Z]/,
        'Password must contain at least one uppercase letter (A-Z)'
      )
      .regex(/[0-9]/, 'Password must contain at least one number (0-9)'),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

const SignUp = () => {
  const navigate = useNavigate();
  const [registerNurse, { isLoading }] = useRegisterNurseMutation();
  const [given_name, setFirstName] = useState('');
  const [middle_name, setMiddleName] = useState('');
  const [family_name, setLastName] = useState('');
  const [phone_number, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showPasswordTips, setShowPasswordTips] = useState({
    password: false,
    confirm: false,
  });

  const handleChange = (field: string, value: string) => {
    setErrors(prev => ({ ...prev, [field]: '' }));

    switch (field) {
      case 'firstName':
        setFirstName(value);
        break;
      case 'middleName':
        setMiddleName(value);
        break;
      case 'lastName':
        setLastName(value);
        break;
      case 'phoneNumber':
        if (!value.startsWith('+91')) {
          value = '+91' + value.replace(/\D/g, '');
        }
        setPhoneNumber(value);
        break;
      case 'email':
        setEmail(value);
        setUsername(value);
        break;
      case 'password':
        setPassword(value);
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationResult = signUpSchema.safeParse({
      given_name,
      middle_name,
      family_name,
      phone_number,
      password,
      confirmPassword,
      email,
    });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });

      setErrors(fieldErrors);
      return;
    }

    try {
      const registrationData: NurseRegistrationData = {
        given_name,
        family_name,
        middle_name,
        phone_number,
        password,
        email,
        username: `${username}`,
      };

      const result = await registerNurse(registrationData).unwrap();
      if (result.success) {
        toast.success('Registration successful! Please verify your OTP.');
        navigate('/verify-otp', { state: { username, phone_number } });
      } else {
        toast.error(result.message || 'Registration failed');
      }
    } catch (error: unknown) {
      console.error('Registration error:', error);

      const isRTKError = (
        err: unknown
      ): err is {
        status: number | string;
        data?: { error?: string; message?: string };
      } => {
        return typeof err === 'object' && err !== null && 'status' in err;
      };

      if (
        isRTKError(error) &&
        (error.status === 409 ||
          error.data?.error === 'Username already exists in Cognito')
      ) {
        toast.error(
          'Email already registered. Please use a different email or try logging in.'
        );
      } else if (
        isRTKError(error) &&
        error.status === 400 &&
        error.data?.error === 'Password does not meet Cognito requirements'
      ) {
        toast.error(
          'Password does not meet security requirements. Please check password criteria.'
        );
      } else if (
        isRTKError(error) &&
        error.status === 400 &&
        error.data?.error === 'Invalid parameter provided to Cognito'
      ) {
        toast.error(
          'Invalid registration details. Please check your inputs and try again.'
        );
      } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
        toast.error(
          'Network error. Please check your connection and try again.'
        );
      } else if (isRTKError(error) && error.status === 500) {
        if (error.data?.error?.includes('database')) {
          toast.error(error.data.error);
        } else {
          toast.error('Server error. Please try again later.');
        }
      } else if (isRTKError(error) && error.data?.message) {
        toast.error(error.data.message);
      } else {
        toast.error('Registration failed. Please try again.');
      }
    }
  };

  return (
    <div className='relative min-h-screen w-full overflow-auto'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-20 z-0' />

      {}
      <div className='relative z-10 min-h-screen w-full flex items-center justify-center px-4 py-6'>
        <div className='w-full max-w-md space-y-0'>
          <div className='flex justify-center scale-75 mb-1'>
            <Logo />
          </div>

          <Card className='bg-white rounded-md p-5 shadow-lg w-full'>
            <h2 className='text-2xl font-bold text-center mb-4 text-nursery-blue'>
              Register as Nurse
            </h2>

            <form onSubmit={handleSubmit} className='space-y-2' noValidate>
              {}
              <div>
                <label htmlFor='firstName' className='text-gray-800 text-sm'>
                  First Name{' '}
                </label>
                <Input
                  id='firstName'
                  type='text'
                  placeholder='Enter Your First Name *'
                  value={given_name}
                  onChange={e => handleChange('firstName', e.target.value)}
                  className='w-full h-10 mt-1'
                />
                {errors.given_name && (
                  <p className='text-red-500 text-sm'>{errors.given_name}</p>
                )}
              </div>

              {}
              <div>
                <label htmlFor='middleName' className='text-gray-800 text-sm'>
                  Middle Name{' '}
                </label>
                <Input
                  id='middleName'
                  type='text'
                  placeholder='Enter Your Middle Name (Optional)'
                  value={middle_name}
                  onChange={e => handleChange('middleName', e.target.value)}
                  className='w-full h-10 mt-1'
                />
              </div>

              {}
              <div>
                <label htmlFor='lastName' className='text-gray-800 text-sm'>
                  Last Name{' '}
                </label>
                <Input
                  id='lastName'
                  type='text'
                  placeholder='Enter Your Last Name *'
                  value={family_name}
                  onChange={e => handleChange('lastName', e.target.value)}
                  className='w-full h-10 mt-1'
                />
                {errors.family_name && (
                  <p className='text-red-500 text-sm'>{errors.family_name}</p>
                )}
              </div>

              {}
              <div>
                <label htmlFor='phoneNumber' className='text-gray-800 text-sm'>
                  Phone Number{' '}
                </label>
                <Input
                  id='phoneNumber'
                  type='tel'
                  placeholder='Enter Your Phone Number *'
                  value={phone_number}
                  onChange={e => handleChange('phoneNumber', e.target.value)}
                  className='w-full h-10 mt-1'
                />
                {errors.phone_number && (
                  <p className='text-red-500 text-sm'>{errors.phone_number}</p>
                )}
              </div>

              {}
              <div>
                <label htmlFor='email' className='text-gray-800 text-sm'>
                  Email{' '}
                </label>
                <Input
                  id='email'
                  type='email'
                  placeholder='Enter Your Email *'
                  value={email}
                  onChange={e => handleChange('email', e.target.value)}
                  className='w-full h-10 mt-1'
                />
                {errors.email && (
                  <p className='text-red-500 text-sm'>{errors.email}</p>
                )}
              </div>

              {}
              <div className='relative'>
                <label htmlFor='password' className='text-gray-800 text-sm'>
                  Password{' '}
                </label>
                <Input
                  id='password'
                  type={showPassword ? 'text' : 'password'}
                  placeholder='Create Password *'
                  value={password}
                  onChange={e => handleChange('password', e.target.value)}
                  className='w-full h-10 mt-1'
                  onFocus={() =>
                    setShowPasswordTips(prev => ({ ...prev, password: true }))
                  }
                  onBlur={() =>
                    setShowPasswordTips(prev => ({
                      ...prev,
                      password: false,
                    }))
                  }
                />
                <Button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  className='bg-transparent absolute right-3 top-7 text-gray-500 hover:bg-transparent hover:text-gray-900 items-center'
                >
                  {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
                </Button>
                {errors.password && (
                  <p className='text-red-500 text-sm'>{errors.password}</p>
                )}
              </div>

              {}
              <div className='relative'>
                <label
                  htmlFor='confirmPassword'
                  className='text-gray-800 text-sm'
                >
                  Confirm Password{' '}
                </label>
                <Input
                  id='confirmPassword'
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder='Re-Enter Password *'
                  value={confirmPassword}
                  onChange={e =>
                    handleChange('confirmPassword', e.target.value)
                  }
                  className='w-full h-10 mt-1 mb-1'
                  onFocus={() =>
                    setShowPasswordTips(prev => ({ ...prev, password: true }))
                  }
                  onBlur={() =>
                    setShowPasswordTips(prev => ({
                      ...prev,
                      password: false,
                    }))
                  }
                />
                <Button
                  type='button'
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className='bg-transparent absolute right-3 top-7 text-gray-500 hover:bg-transparent hover:text-gray-900'
                >
                  {showConfirmPassword ? (
                    <Eye size={20} />
                  ) : (
                    <EyeOff size={20} />
                  )}
                </Button>
                {errors.confirmPassword && (
                  <p className='text-red-500 text-sm'>
                    {errors.confirmPassword}
                  </p>
                )}
              </div>

              {}
              {(showPasswordTips.password || showPasswordTips.confirm) && (
                <div className='mt-6 p-4 bg-nursery-blue bg-opacity-30 rounded-lg border border-nursery-blue'>
                  <h3 className='text-sm font-medium text-nursery-darkBlue mb-2'>
                    Password Tips:
                  </h3>
                  <ul className='text-xs text-nursery-darkBlue space-y-1'>
                    <li>
                      • Password must be at least 8 characters and include at
                      least one special character, one uppercase letter, and one
                      number.
                    </li>
                  </ul>
                </div>
              )}

              {}
              <Button
                type='submit'
                className='w-full h-10 bg-[#4AB4CE] text-white'
                disabled={isLoading}
              >
                {isLoading ? 'Registering...' : 'Register'}
              </Button>
              <Button
                type='button'
                variant='outline'
                onClick={() => navigate('/')}
                className='w-full h-10 mt-10 border-[#4AB4CE] text-[#4AB4CE]'
              >
                Cancel
              </Button>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
