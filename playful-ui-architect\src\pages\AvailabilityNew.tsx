import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/utils/toast';
import {
  useCreateMultipleAvailabilitiesMutation,
  useGetMyAvailabilitiesQuery,
  useGetTimeSlotsQuery,
  TimeSlot,
} from '../store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import AvailabilityCalendar from '@/components/AvailabilityCalendar';
import AvailabilitySummary from '@/components/AvailabilitySummary';
import bg from '../../public/Images/bg4.png';

const useSlotManagement = (
  processedTimeSlots: TimeSlot[],
  existingAvailabilityData: { availability_slots?: AvailabilitySlot[] } | null
) => {
  const [slotBookingStatus, setSlotBookingStatus] = useState<SlotBookingStatus>(
    {}
  );
  const [existingSlots, setExistingSlots] = useState<SlotsByDate>({});
  const [newlySelectedSlots, setNewlySelectedSlots] = useState<SlotsByDate>({});

  const processAvailabilitySlot = useCallback(
    (
      slot: AvailabilitySlot,
      processedSlots: SlotsByDate,
      bookingStatus: SlotBookingStatus
    ) => {
      const dateKey = slot.availability_date;

      if (!processedSlots[dateKey]) {
        processedSlots[dateKey] = [];
        bookingStatus[dateKey] = {};
      }

      slot.availability_slots.forEach((time: string) => {
        const displayTime = timeUtils.convertTimeToDisplayFormat(time);

        if (!processedSlots[dateKey].includes(displayTime)) {
          processedSlots[dateKey].push(displayTime);
        }

        const matchingSlot = processedTimeSlots.find(
          timeSlot => timeSlot.display_time === displayTime
        );

        if (matchingSlot) {
          bookingStatus[dateKey][matchingSlot.id] = true;
        }
      });
    },
    [processedTimeSlots]
  );

  const sortProcessedSlots = useCallback(
    (processedSlots: SlotsByDate) => {
      Object.keys(processedSlots).forEach(dateKey => {
        processedSlots[dateKey] = slotUtils.sortSlotsByTime(
          processedSlots[dateKey],
          processedTimeSlots
        );
      });
    },
    [processedTimeSlots]
  );

  useEffect(() => {
    if (
      !existingAvailabilityData?.availability_slots ||
      !processedTimeSlots.length
    ) {
      return;
    }

    const processedSlots: SlotsByDate = {};
    const bookingStatus: SlotBookingStatus = {};

    existingAvailabilityData.availability_slots.forEach(
      (slot: AvailabilitySlot) => {
        processAvailabilitySlot(slot, processedSlots, bookingStatus);
      }
    );

    sortProcessedSlots(processedSlots);

    setExistingSlots(processedSlots);
    setSlotBookingStatus(bookingStatus);
  }, [
    existingAvailabilityData,
    processedTimeSlots,
    processAvailabilitySlot,
    sortProcessedSlots,
  ]);

  return {
    slotBookingStatus,
    setSlotBookingStatus,
    existingSlots,
    newlySelectedSlots,
    setNewlySelectedSlots,
  };
};

const MONTH_NAMES = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
] as const;

const DAY_NAMES = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as const;

const FARE_REGEX = /^(?:\d+(?:\.\d*)?|\.\d+)$/;

const CALENDAR_CONSTANTS = {
  MAX_FARE_LENGTH: 20,
  HOURS_IN_DAY: 24,
  MINUTES_IN_HOUR: 60,
  NOON_HOUR: 12,
  MIDNIGHT_HOUR: 0,
} as const;

const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
} as const;

interface DateState {
  currentMonth: number;
  currentYear: number;
  selectedDate: Date | null;
}

interface FareState {
  fare: string;
  fareError: string;
  isFocused: boolean;
}

interface SlotBookingStatus {
  [dateKey: string]: {
    [slotId: string]: boolean;
  };
}

interface SlotsByDate {
  [dateKey: string]: string[];
}

interface AvailabilitySlot {
  availability_date: string;
  availability_slots: string[];
  hourly_fare: number;
}

interface ApiSuccessItem {
  availability_date: string;
  availability_time_slots?: string[];
}

const timeUtils = {
  getTomorrow: (): Date => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  },

  convertTimeToDisplayFormat: (timeString: string): string => {
    if (!timeString) return '';

    try {
      const time24h = timeString.includes('T')
        ? new Date(timeString).toTimeString().substring(0, 5)
        : timeString.substring(0, 5);

      if (!/^\d{2}:\d{2}$/.test(time24h)) {
        return timeString;
      }

      const [hours, minutes] = time24h.split(':');
      const hour24 = parseInt(hours, 10);
      const hour12 = hour24 % 12 || 12;
      const ampm = hour24 >= 12 ? 'PM' : 'AM';

      return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`;
    } catch (error) {
      console.warn('Failed to convert time format:', timeString, error);
      return timeString;
    }
  },

  getSortOrderFromTime: (displayTime: string): number => {
    if (!displayTime) return 0;

    const [time, ampm] = displayTime.split(' ');
    if (!time || !ampm) return 0;

    const [hours, minutes] = time.split(':');
    if (!hours || !minutes) return 0;

    let hour24 = parseInt(hours, 10);
    if (ampm === 'AM' && hour24 === CALENDAR_CONSTANTS.NOON_HOUR)
      hour24 = CALENDAR_CONSTANTS.MIDNIGHT_HOUR;
    if (ampm === 'PM' && hour24 !== CALENDAR_CONSTANTS.NOON_HOUR)
      hour24 += CALENDAR_CONSTANTS.NOON_HOUR;

    return hour24 * CALENDAR_CONSTANTS.MINUTES_IN_HOUR + parseInt(minutes, 10);
  },

  convertTo24Hour: (time12h: string): string => {
    if (!time12h) return '';

    const [time, modifier] = time12h.split(' ');
    if (!time || !modifier) return time12h;

    const [hours, minutes] = time.split(':');
    if (!hours || !minutes) return time12h;

    let hour24 = parseInt(hours, 10);
    if (modifier === 'AM' && hour24 === CALENDAR_CONSTANTS.NOON_HOUR)
      hour24 = CALENDAR_CONSTANTS.MIDNIGHT_HOUR;
    else if (modifier === 'PM' && hour24 !== CALENDAR_CONSTANTS.NOON_HOUR)
      hour24 += CALENDAR_CONSTANTS.NOON_HOUR;

    return `${hour24.toString().padStart(2, '0')}:${minutes}`;
  },

  formatDateKey: (date: Date): string => {
    if (!date || !(date instanceof Date)) return '';

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  isDateDisabled: (date: Date): boolean => {
    if (!date || !(date instanceof Date)) return true;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const incoming = new Date(date);
    incoming.setHours(0, 0, 0, 0);
    return incoming <= today;
  },

  isValidFare: (value: string): boolean => {
    if (!value) return true;

    if (value.length > CALENDAR_CONSTANTS.MAX_FARE_LENGTH) return false;

    return FARE_REGEX.test(value);
  },
};

const slotUtils = {
  findSlotByDisplayTime: (
    timeSlots: TimeSlot[],
    displayTime: string
  ): TimeSlot | undefined => {
    return timeSlots.find(slot => slot.display_time === displayTime);
  },

  sortSlotsByTime: (slots: string[], timeSlots: TimeSlot[]): string[] => {
    return slots.sort((a, b) => {
      const aSlot = slotUtils.findSlotByDisplayTime(timeSlots, a);
      const bSlot = slotUtils.findSlotByDisplayTime(timeSlots, b);
      return (aSlot?.sort_order || 0) - (bSlot?.sort_order || 0);
    });
  },

  removeSlotFromDate: (
    slots: SlotsByDate,
    dateKey: string,
    timeSlot: string
  ): SlotsByDate => {
    const current = slots[dateKey] || [];
    const updated = current.filter(slot => slot !== timeSlot);

    if (updated.length === 0) {
      const { [dateKey]: _removed, ...rest } = slots;
      return rest;
    }

    return { ...slots, [dateKey]: updated };
  },

  addSlotToDate: (
    slots: SlotsByDate,
    dateKey: string,
    timeSlot: string,
    timeSlots: TimeSlot[]
  ): SlotsByDate => {
    const current = slots[dateKey] || [];
    const newSlots = slotUtils.sortSlotsByTime(
      [...current, timeSlot],
      timeSlots
    );
    return { ...slots, [dateKey]: newSlots };
  },
};

const handleApiError = (error: unknown, context: string): void => {
  console.error(`${context}:`, error);

  const apiError = error as {
    status?: number | string;
    data?: { error?: string; message?: string };
  };

  if (apiError?.status === 'FETCH_ERROR') {
    toast.error('Network error. Please check your connection.');
  } else if (apiError?.status === 404) {
    toast.error('Resource not found. Please try again.');
  } else if (apiError?.status === 500) {
    toast.error('Server error. Please try again later.');
  } else {
    const errorMessage =
      apiError?.data?.message ||
      apiError?.data?.error ||
      'An unexpected error occurred';
    toast.error(errorMessage);
  }
};

const validationUtils = {
  validateFareInput: (fare: string): { isValid: boolean; error: string } => {
    if (!fare || fare.trim() === '' || parseFloat(fare) <= 0) {
      return {
        isValid: false,
        error: 'Please enter your Fare at the top.',
      };
    }
    return { isValid: true, error: '' };
  },

  validateSlotSelection: (
    slots: SlotsByDate
  ): { isValid: boolean; error: string } => {
    if (Object.keys(slots).length === 0) {
      return {
        isValid: false,
        error: 'Please select at least one new available time slot.',
      };
    }
    return { isValid: true, error: '' };
  },
};

const AvailabilityNew: React.FC = () => {
  const [createMultipleAvailabilities, { isLoading: isCreating }] =
    useCreateMultipleAvailabilitiesMutation();
  const {
    data: existingAvailabilityData,
    isLoading: isLoadingExisting,
    error: availabilitiesError,
    refetch,
  } = useGetMyAvailabilitiesQuery();
  const {
    data: timeSlotsData,
    isLoading: isLoadingTimeSlots,
    error: timeSlotsError,
  } = useGetTimeSlotsQuery();

  const [dateState, setDateState] = useState<DateState>(() => {
    const tomorrow = timeUtils.getTomorrow();
    return {
      currentMonth: tomorrow.getMonth(),
      currentYear: tomorrow.getFullYear(),
      selectedDate: null,
    };
  });

  const [fareState, setFareState] = useState<FareState>({
    fare: '',
    fareError: '',
    isFocused: false,
  });

  const navigate = useNavigate();

  const processedTimeSlots = useMemo((): TimeSlot[] => {
    if (!timeSlotsData?.success || !Array.isArray(timeSlotsData.data)) {
      return [];
    }

    return timeSlotsData.data
      .filter(slot => slot?.id && slot?.time_slot)
      .map((slot: { id: string; time_slot: string }) => {
        const displayTime = timeUtils.convertTimeToDisplayFormat(
          slot.time_slot
        );
        return {
          id: slot.id.toString(),
          time_slot: slot.time_slot,
          display_time: displayTime,
          sort_order: timeUtils.getSortOrderFromTime(displayTime),
        };
      })
      .sort((a, b) => a.sort_order - b.sort_order);
  }, [timeSlotsData]);

  const {
    slotBookingStatus,
    setSlotBookingStatus,
    existingSlots,
    newlySelectedSlots,
    setNewlySelectedSlots,
  } = useSlotManagement(processedTimeSlots, existingAvailabilityData);

  const hasExistingAvailability = useMemo(() => {
    return Boolean(existingAvailabilityData?.availability_slots?.length);
  }, [existingAvailabilityData]);

  const shouldDisableFareInput = useMemo(() => {
    return hasExistingAvailability || isCreating;
  }, [hasExistingAvailability, isCreating]);

  useEffect(() => {
    if (existingAvailabilityData?.availability_slots?.length > 0) {
      const existingFare =
        existingAvailabilityData.availability_slots[0].hourly_fare.toString();
      setFareState(prev => ({ ...prev, fare: existingFare }));
    }
  }, [existingAvailabilityData]);

  useEffect(() => {
    if (availabilitiesError) {
      handleApiError(availabilitiesError, 'Failed to load availabilities');
    }
  }, [availabilitiesError]);

  useEffect(() => {
    if (timeSlotsError) {
      handleApiError(timeSlotsError, 'Failed to load time slots');
    }
  }, [timeSlotsError]);

  const navigateMonth = useCallback((direction: 'prev' | 'next') => {
    setDateState(prev => {
      const newState = { ...prev };

      if (direction === 'prev') {
        if (prev.currentMonth === 0) {
          newState.currentMonth = 11;
          newState.currentYear = prev.currentYear - 1;
        } else {
          newState.currentMonth = prev.currentMonth - 1;
        }
      } else {
        if (prev.currentMonth === 11) {
          newState.currentMonth = 0;
          newState.currentYear = prev.currentYear + 1;
        } else {
          newState.currentMonth = prev.currentMonth + 1;
        }
      }

      return newState;
    });
  }, []);

  const handleFareChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (shouldDisableFareInput) {
        return;
      }

      const value = e.target.value;

      if (timeUtils.isValidFare(value)) {
        setFareState(prev => ({
          ...prev,
          fare: value,
          fareError: prev.fareError ? '' : prev.fareError,
        }));
      }
    },
    [shouldDisableFareInput]
  );

  const isSlotAlreadyBooked = useCallback(
    (dateKey: string, timeSlot: string): boolean => {
      const matchingSlot = processedTimeSlots.find(
        slot => slot.display_time === timeSlot
      );
      return matchingSlot
        ? Boolean(slotBookingStatus[dateKey]?.[matchingSlot.id])
        : false;
    },
    [processedTimeSlots, slotBookingStatus]
  );

  const toggleSlot = useCallback(
    (dateKey: string, timeSlot: string) => {
      if (isSlotAlreadyBooked(dateKey, timeSlot)) {
        toast.info('This slot is already booked and cannot be modified.');
        return;
      }

      setNewlySelectedSlots(prev => {
        const current = prev[dateKey] || [];
        const isSelected = current.includes(timeSlot);

        return isSelected
          ? slotUtils.removeSlotFromDate(prev, dateKey, timeSlot)
          : slotUtils.addSlotToDate(
              prev,
              dateKey,
              timeSlot,
              processedTimeSlots
            );
      });
    },
    [isSlotAlreadyBooked, processedTimeSlots, setNewlySelectedSlots]
  );

  const toggleAllSlots = useCallback(
    (dateKey: string) => {
      const availableSlots = processedTimeSlots
        .map(slot => slot.display_time)
        .filter(timeSlot => !isSlotAlreadyBooked(dateKey, timeSlot));

      const currentNewSlots = newlySelectedSlots[dateKey] || [];
      const allSelected = currentNewSlots.length === availableSlots.length;

      setNewlySelectedSlots(prev => {
        if (allSelected) {
          const { [dateKey]: _removed, ...rest } = prev;
          return rest;
        } else {
          return { ...prev, [dateKey]: [...availableSlots] };
        }
      });
    },
    [
      processedTimeSlots,
      isSlotAlreadyBooked,
      newlySelectedSlots,
      setNewlySelectedSlots,
    ]
  );

  const handleDateClick = useCallback(
    (day: number) => {
      const date = new Date(dateState.currentYear, dateState.currentMonth, day);
      if (!timeUtils.isDateDisabled(date)) {
        setDateState(prev => ({ ...prev, selectedDate: date }));
      }
    },
    [dateState.currentYear, dateState.currentMonth]
  );

  const getDayClassName = useCallback(
    (
      isDisabled: boolean,
      isSelected: boolean,
      hasExistingSlots: boolean,
      hasNewSlots: boolean
    ): string => {
      const baseClassName =
        'p-2 text-center cursor-pointer rounded-lg border transition-colors relative ';

      if (isDisabled) {
        return baseClassName + 'bg-gray-200 text-gray-400 cursor-not-allowed';
      }

      if (isSelected) {
        return baseClassName + 'bg-nursery-blue text-white border-nursery-blue';
      }

      if (hasExistingSlots && hasNewSlots) {
        return (
          baseClassName +
          'bg-purple-100 border-purple-300 text-purple-800 hover:bg-purple-200'
        );
      }

      if (hasExistingSlots) {
        return (
          baseClassName +
          'bg-green-100 border-green-300 text-green-800 hover:bg-green-200'
        );
      }

      if (hasNewSlots) {
        return (
          baseClassName +
          'bg-blue-100 border-blue-300 text-blue-800 hover:bg-blue-200'
        );
      }

      return baseClassName + 'bg-white hover:bg-gray-200 border-gray-200';
    },
    []
  );

  const handleDayKeyDown = useCallback(
    (e: React.KeyboardEvent, day: number) => {
      if (e.key === KEYBOARD_KEYS.ENTER || e.key === KEYBOARD_KEYS.SPACE) {
        e.preventDefault();
        handleDateClick(day);
      }
    },
    [handleDateClick]
  );

  const renderSlotIndicators = useCallback(
    (hasExistingSlots: boolean, hasNewSlots: boolean, isSelected: boolean) => (
      <>
        {hasExistingSlots && !isSelected && (
          <div className='absolute top-1 right-1 w-2 h-2 bg-green-500 rounded-full' />
        )}
        {hasNewSlots && !isSelected && (
          <div className='absolute top-1 left-1 w-2 h-2 bg-blue-500 rounded-full' />
        )}
      </>
    ),
    []
  );

  const renderSingleDay = useCallback(
    (day: number) => {
      const date = new Date(dateState.currentYear, dateState.currentMonth, day);
      const dateKey = timeUtils.formatDateKey(date);
      const isDisabled = timeUtils.isDateDisabled(date);
      const isSelected =
        dateState.selectedDate?.getDate() === day &&
        dateState.selectedDate?.getMonth() === dateState.currentMonth &&
        dateState.selectedDate?.getFullYear() === dateState.currentYear;

      const hasExistingSlots = (existingSlots[dateKey]?.length || 0) > 0;
      const hasNewSlots = (newlySelectedSlots[dateKey]?.length || 0) > 0;
      const dayClassName = getDayClassName(
        isDisabled,
        isSelected,
        hasExistingSlots,
        hasNewSlots
      );

      return (
        <div
          key={day}
          onClick={() => handleDateClick(day)}
          className={dayClassName}
          role='button'
          tabIndex={isDisabled ? -1 : 0}
          aria-label={`Select date ${day}`}
          aria-disabled={isDisabled}
          onKeyDown={e => handleDayKeyDown(e, day)}
        >
          {day}
          {renderSlotIndicators(hasExistingSlots, hasNewSlots, isSelected)}
        </div>
      );
    },
    [
      dateState,
      existingSlots,
      newlySelectedSlots,
      handleDateClick,
      getDayClassName,
      handleDayKeyDown,
      renderSlotIndicators,
    ]
  );

  const renderCalendarDays = useCallback(() => {
    const daysInMonth = new Date(
      dateState.currentYear,
      dateState.currentMonth + 1,
      0
    ).getDate();
    const firstDay = new Date(
      dateState.currentYear,
      dateState.currentMonth,
      1
    ).getDay();

    const days: JSX.Element[] = [];

    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className='p-2' />);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      days.push(renderSingleDay(day));
    }

    return days;
  }, [dateState, renderSingleDay]);

  const convertNewSlotsToAPIFormat = useCallback(() => {
    return Object.entries(newlySelectedSlots).map(([dateKey, timeSlots]) => ({
      availability_date: dateKey,
      availability_timeSlots: timeSlots.map(timeUtils.convertTo24Hour),
      hourly_fare: parseFloat(fareState.fare),
    }));
  }, [newlySelectedSlots, fareState.fare]);

  const updateBookingStatusForItem = useCallback(
    (item: ApiSuccessItem, updated: SlotBookingStatus) => {
      const dateKey = item.availability_date;
      if (!updated[dateKey]) {
        updated[dateKey] = {};
      }

      item.availability_time_slots?.forEach((slot: string) => {
        const displayTime = timeUtils.convertTimeToDisplayFormat(slot);
        const matchingSlot = processedTimeSlots.find(
          timeSlot => timeSlot.display_time === displayTime
        );
        if (matchingSlot) {
          updated[dateKey][matchingSlot.id] = true;
        }
      });
    },
    [processedTimeSlots]
  );

  const handleSuccessfulSave = useCallback(
    async (result: {
      summary: { successful_dates: number; failed_dates: number };
      successful?: ApiSuccessItem[];
      failed?: unknown;
    }) => {
      if (result.summary.successful_dates > 0) {
        toast.success(
          `Successfully created/updated availability for ${result.summary.successful_dates} day(s)!`
        );

        setSlotBookingStatus(prev => {
          const updated = { ...prev };
          result.successful?.forEach((item: ApiSuccessItem) => {
            updateBookingStatusForItem(item, updated);
          });
          return updated;
        });

        setNewlySelectedSlots({});
        setDateState(prev => ({ ...prev, selectedDate: null }));
        await refetch();
      }

      if (result.summary.failed_dates > 0) {
        toast.error(
          `${result.summary.failed_dates} day(s) failed to create/update.`
        );
        console.warn('Failed slots:', result.failed);
      }
    },
    [
      refetch,
      updateBookingStatusForItem,
      setSlotBookingStatus,
      setNewlySelectedSlots,
    ]
  );

  const handleSaveAvailability = useCallback(async () => {
    setFareState(prev => ({ ...prev, fareError: '' }));

    const fareValidation = validationUtils.validateFareInput(fareState.fare);
    if (!fareValidation.isValid) {
      setFareState(prev => ({ ...prev, fareError: fareValidation.error }));
      return;
    }

    const slotValidation =
      validationUtils.validateSlotSelection(newlySelectedSlots);
    if (!slotValidation.isValid) {
      toast.error(slotValidation.error);
      return;
    }

    try {
      const apiSlots = convertNewSlotsToAPIFormat();
      const result = await createMultipleAvailabilities({
        availability_slots: apiSlots,
      }).unwrap();

      await handleSuccessfulSave(result);
    } catch (error) {
      handleApiError(error, 'Error creating availability slots');
    }
  }, [
    fareState.fare,
    newlySelectedSlots,
    convertNewSlotsToAPIFormat,
    createMultipleAvailabilities,
    handleSuccessfulSave,
  ]);

  const computedValues = useMemo(() => {
    const selectedDateKey = dateState.selectedDate
      ? timeUtils.formatDateKey(dateState.selectedDate)
      : null;

    const selectedDateNewSlots = selectedDateKey
      ? newlySelectedSlots[selectedDateKey] || []
      : [];

    const selectedDateExistingSlots = selectedDateKey
      ? existingSlots[selectedDateKey] || []
      : [];

    const newSlotStats = {
      total: Object.values(newlySelectedSlots).reduce(
        (sum, slots) => sum + slots.length,
        0
      ),
      days: Object.keys(newlySelectedSlots).length,
    };

    const existingSlotStats = {
      total: Object.values(existingSlots).reduce(
        (sum, slots) => sum + slots.length,
        0
      ),
      days: Object.keys(existingSlots).length,
    };

    const fareValue = parseFloat(fareState.fare) || 0;
    const earnings = {
      potential: (fareValue * newSlotStats.total).toFixed(2),
      existing: (fareValue * existingSlotStats.total).toFixed(2),
    };

    return {
      selectedDateKey,
      selectedDateNewSlots,
      selectedDateExistingSlots,
      newSlotStats,
      existingSlotStats,
      earnings,
    };
  }, [
    dateState.selectedDate,
    newlySelectedSlots,
    existingSlots,
    fareState.fare,
  ]);

  if (isLoadingExisting || isLoadingTimeSlots) {
    return (
      <div className='flex items-center justify-center p-4'>
        <ResponsiveLoader />
      </div>
    );
  }

  const timeSlots = processedTimeSlots.map(slot => slot.display_time);

  return (
    <div className='max-w-full min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1'>
          <button
            onClick={() => navigate(-1)}
            className='mr-3'
            type='button'
            aria-label='Go back'
          >
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Set Availability Slots</h1>
        </div>
      </header>

      <main className='flex-1 px-4 pt-6 pb-8'>
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20 md:mb-0'>
          <AvailabilityCalendar
            monthNames={[...MONTH_NAMES]}
            currentMonth={dateState.currentMonth}
            currentYear={dateState.currentYear}
            goToPreviousMonth={() => navigateMonth('prev')}
            goToNextMonth={() => navigateMonth('next')}
            dayNames={[...DAY_NAMES]}
            renderCalendarDays={renderCalendarDays}
            fare={fareState.fare}
            handleFareChange={handleFareChange}
            isFocused={fareState.isFocused}
            setIsFocused={(focused: boolean) =>
              setFareState(prev => ({ ...prev, isFocused: focused }))
            }
            _isCreating={isCreating}
            shouldDisableFareInput={shouldDisableFareInput}
            hasExistingAvailability={hasExistingAvailability}
          />

          <AvailabilitySummary
            selectedDate={dateState.selectedDate}
            selectedDateKey={computedValues.selectedDateKey}
            selectedDateNewSlots={computedValues.selectedDateNewSlots}
            selectedDateExistingSlots={computedValues.selectedDateExistingSlots}
            timeSlots={timeSlots}
            isSlotAlreadyBooked={isSlotAlreadyBooked}
            toggleSlot={toggleSlot}
            toggleAllSlots={toggleAllSlots}
            isCreating={isCreating}
            newlySelectedSlots={newlySelectedSlots}
            fare={fareState.fare}
            fareError={fareState.fareError}
            handleSaveAvailability={handleSaveAvailability}
            totalNewSlots={computedValues.newSlotStats.total}
            totalNewDays={computedValues.newSlotStats.days}
            potentialEarnings={computedValues.earnings.potential}
            existingSlots={existingSlots}
          />
        </div>
      </main>
    </div>
  );
};

export default AvailabilityNew;
