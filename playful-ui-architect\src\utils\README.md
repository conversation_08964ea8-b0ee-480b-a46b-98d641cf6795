# Navigation State Utility

This utility provides centralized functions and hooks for creating navigation state objects throughout the application.

## Overview

The navigation state utility eliminates code duplication by providing reusable functions and hooks for creating navigation state objects that are commonly used when navigating between pages in the app.

## Files

- `src/utils/navigationState.ts` - Core utility functions
- `src/hooks/useNavigationState.ts` - Custom hooks for React components

## Usage

### 1. Using the Custom Hook (Recommended)

```tsx
import { useNavigationState } from '@/hooks/useNavigationState';

const MyComponent = () => {
  const { navigationState, isLoading } = useNavigationState();

  const navigate = useNavigate();

  const handleNavigate = () => {
    navigate('/home', { state: navigationState });
  };

  if (isLoading) return <div>Loading...</div>;

  return <button onClick={handleNavigate}>Navigate to Home</button>;
};
```

### 2. Using with Fallback Data

```tsx
import { useNavigationState } from '@/hooks/useNavigationState';

const MyComponent = () => {
  const { navigationState } = useNavigationState({
    username: '<EMAIL>',
    givenName: 'Fallback User',
    address: 'Fallback Address',
    userId: 'fallback-id',
  });

  // Use navigationState...
};
```

### 3. Using Utility Functions Directly

```tsx
import { createNavigationStateFromParams } from '@/utils/navigationState';

const MyComponent = () => {
  const navigationState = createNavigationStateFromParams(
    '<EMAIL>',
    'John Doe',
    '123 Main St',
    'user-id-123'
  );

  // Use navigationState...
};
```

### 4. Using with Profile Details

```tsx
import { createNavigationState } from '@/utils/navigationState';

const MyComponent = () => {
  const { data: profileDetails } = useGetProfileDetailsQuery();

  const navigationState = createNavigationState(profileDetails.details);

  // Use navigationState...
};
```

## API Reference

### Hooks

#### `useNavigationState(fallbackData?)`

Returns an object with:

- `navigationState`: NavigationState object or null
- `isLoading`: Boolean indicating if profile data is loading
- `profileDetails`: Raw profile details from API

#### `useNavigationStateFromParams(username, givenName, address, userId)`

Returns a NavigationState object created from individual parameters.

### Utility Functions

#### `createNavigationState(profileDetails)`

Creates a NavigationState object from a ProfileDetails object.

#### `createNavigationStateFromParams(username, givenName, address, userId)`

Creates a NavigationState object from individual parameters.

#### `createNavigationStateWithFallback(profileDetails, fallbackData?)`

Creates a NavigationState object with fallback values for missing data.

## NavigationState Interface

```typescript
interface NavigationState {
  username: string;
  given_name: string;
  address: string;
  user_id: string;
  nurse_set_location: boolean;
}
```

## Migration Guide

### Before (Old Pattern)

```tsx
// Helper function to create navigation state
const createNavigationState = (
  username: string,
  givenName: string,
  address: string,
  userId: string
) => ({
  username,
  given_name: givenName,
  address,
  user_id: userId,
  nurse_set_location: true,
});

const navigationState = createNavigationState(
  profileDetails.details.email,
  profileDetails.details.given_name,
  profileDetails.details.address,
  profileDetails.details.user_id
);
```

### After (New Pattern)

```tsx
import { useNavigationState } from '@/hooks/useNavigationState';

const { navigationState } = useNavigationState();
```

## Benefits

1. **DRY Principle**: Eliminates code duplication across components
2. **Type Safety**: Provides TypeScript interfaces for better development experience
3. **Consistency**: Ensures all navigation states follow the same structure
4. **Maintainability**: Centralized logic makes updates easier
5. **Reusability**: Can be used across the entire application
6. **Error Handling**: Built-in fallback mechanisms for missing data
