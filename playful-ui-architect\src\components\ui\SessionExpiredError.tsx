import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Shield } from 'lucide-react';
import { performLogout } from '@/utils/logout';

interface SessionExpiredErrorProps {
  className?: string;
}

const SessionExpiredError: React.FC<SessionExpiredErrorProps> = ({
  className = '',
}) => {
  const navigate = useNavigate();

  const handleReturnToLogin = () => {
    performLogout();
    navigate('/login');
  };

  return (
    <div
      className={`min-h-screen flex items-center justify-center bg-[#F2F2F2] p-5 ${className}`}
    >
      <div className='max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center'>
        <div className='mb-6'>
          <div className='mx-auto w-10 h-10 bg-white rounded-full flex items-center justify-center '>
            <Shield className='h-7 w-7 text-red-500' />
          </div>
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Session Expired
          </h2>
          <p className='text-gray-600 mb-3'>
            Your session has expired for security reasons. Please log in again
            to continue.
          </p>
        </div>
        <button
          onClick={handleReturnToLogin}
          className='w-full bg-[#4AB4CE] hover:bg-[#3a9bb5] text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200'
        >
          Return to Login Page
        </button>
      </div>
    </div>
  );
};

export default SessionExpiredError;
