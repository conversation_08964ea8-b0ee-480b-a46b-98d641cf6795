import { useState } from 'react';
import { toast, toastUtils } from '@/utils/toast';
import { Booking } from '@/store/api/customerApiSlice';

interface ServiceStatusResponse {
  data?: {
    started_at?: string;
    completed_at?: string;
    payment_received_at?: string;
  };
}

interface ServiceStatus {
  status: string;
  started_at?: string;
  completed_at?: string;
  payment_received_at?: string;
}

interface ServiceStatusMap {
  [bookingId: string]: ServiceStatus;
}

interface BookingActionsOptions {
  onUpdateBookingStatus?: (
    bookingId: string,
    status: string,
    reason?: string
  ) => Promise<ServiceStatusResponse>;
  onUpdateServiceStatus?: (
    bookingId: string,
    status: string
  ) => Promise<ServiceStatusResponse>;
  setServiceStatus?: (
    updater: (prev: ServiceStatusMap) => ServiceStatusMap
  ) => void;
  setSelectedTab?: (tab: string) => void;
}

interface DeclineFormState {
  isOpen: boolean;
  reason: string;
  error: string;
  isSubmitting: boolean;
}

interface UseBookingActionsReturn {
  declineForm: DeclineFormState;
  openDeclineForm: (bookingId: string) => void;
  closeDeclineForm: () => void;
  updateDeclineReason: (reason: string) => void;
  submitDecline: (bookingId: string) => Promise<void>;

  acceptBooking: (bookingId: string) => Promise<void>;
  declineBooking: (bookingId: string) => Promise<void>;
  completeBooking: (bookingId: string) => Promise<void>;

  startService: (bookingId: string) => Promise<void>;
  endService: (bookingId: string) => Promise<void>;
  markPaymentReceived: (bookingId: string) => Promise<void>;

  getStatusBadgeClasses: (status: string) => string;
  canExpand: (booking: Booking) => boolean;
}

export const useBookingActions = (
  options: BookingActionsOptions = {}
): UseBookingActionsReturn => {
  const {
    onUpdateBookingStatus,
    onUpdateServiceStatus,
    setServiceStatus,
    setSelectedTab,
  } = options;

  const [declineForm, setDeclineForm] = useState<DeclineFormState>({
    isOpen: false,
    reason: '',
    error: '',
    isSubmitting: false,
  });

  const openDeclineForm = (_bookingId: string) => {
    setDeclineForm({
      isOpen: true,
      reason: '',
      error: '',
      isSubmitting: false,
    });
  };

  const closeDeclineForm = () => {
    setDeclineForm({
      isOpen: false,
      reason: '',
      error: '',
      isSubmitting: false,
    });
  };

  const updateDeclineReason = (reason: string) => {
    setDeclineForm(prev => ({
      ...prev,
      reason,
      error: '',
    }));
  };

  const submitDecline = async (bookingId: string) => {
    if (!declineForm.reason.trim()) {
      setDeclineForm(prev => ({
        ...prev,
        error: 'Please provide a reason for declining',
      }));
      return;
    }

    if (declineForm.reason.trim().length < 50) {
      setDeclineForm(prev => ({
        ...prev,
        error: 'Reason must be at least 50 characters long',
      }));
      return;
    }

    setDeclineForm(prev => ({ ...prev, isSubmitting: true, error: '' }));

    try {
      if (onUpdateBookingStatus) {
        await onUpdateBookingStatus(
          bookingId,
          'Declined',
          declineForm.reason.trim()
        );
      }
      closeDeclineForm();
      toastUtils.successAlt('Booking declined successfully!');
    } catch (error) {
      console.error('Failed to decline booking:', error);
      setDeclineForm(prev => ({
        ...prev,
        error: 'Failed to decline booking. Please try again.',
      }));
      toast.error('Failed to decline booking. Please try again.');
    } finally {
      setDeclineForm(prev => ({ ...prev, isSubmitting: false }));
    }
  };

  const acceptBooking = async (bookingId: string) => {
    try {
      if (onUpdateBookingStatus) {
        await onUpdateBookingStatus(bookingId, 'Accepted');
      }
      toastUtils.successAlt('Booking accepted successfully!');
    } catch (error) {
      console.error('Failed to accept booking:', error);
      handleBookingError(error);
    }
  };

  const declineBooking = async (bookingId: string) => {
    openDeclineForm(bookingId);
  };

  const completeBooking = async (bookingId: string) => {
    try {
      if (onUpdateBookingStatus) {
        await onUpdateBookingStatus(bookingId, 'Completed');
      }
      toastUtils.successAlt('Booking marked as completed!');
      if (setSelectedTab) {
        setSelectedTab('Complete');
      }
    } catch (error) {
      console.error('Failed to complete booking:', error);
      handleBookingError(error);
    }
  };

  const startService = async (bookingId: string) => {
    try {
      if (onUpdateServiceStatus) {
        const response = await onUpdateServiceStatus(bookingId, 'started');

        if (setServiceStatus) {
          setServiceStatus(prev => ({
            ...prev,
            [bookingId]: {
              ...prev[bookingId],
              status: 'started',
              started_at:
                response?.data?.started_at || new Date().toISOString(),
            },
          }));
        }
      }
      toastUtils.successAlt('Service started successfully!');
    } catch (error) {
      console.error('Failed to start service:', error);
      toast.error('Failed to start service. Please try again.');
    }
  };

  const endService = async (bookingId: string) => {
    try {
      if (onUpdateServiceStatus && onUpdateBookingStatus) {
        const response = await onUpdateServiceStatus(bookingId, 'completed');

        if (setServiceStatus) {
          setServiceStatus(prev => ({
            ...prev,
            [bookingId]: {
              ...prev[bookingId],
              status: 'completed',
              completed_at:
                response?.data?.completed_at || new Date().toISOString(),
            },
          }));
        }

        await onUpdateBookingStatus(bookingId, 'Completed');
      }
      toastUtils.successAlt('Service completed successfully!');
    } catch (error) {
      console.error('Failed to complete service:', error);
      toast.error('Failed to complete service. Please try again.');
    }
  };

  const markPaymentReceived = async (bookingId: string) => {
    try {
      if (onUpdateServiceStatus) {
        const response = await onUpdateServiceStatus(
          bookingId,
          'payment_received'
        );

        if (setServiceStatus) {
          setServiceStatus(prev => ({
            ...prev,
            [bookingId]: {
              ...prev[bookingId],
              status: 'payment_received',
              payment_received_at:
                response?.data?.payment_received_at || new Date().toISOString(),
            },
          }));
        }
      }
      toastUtils.successAlt('Payment marked as received!');
    } catch (error) {
      console.error('Failed to mark payment received:', error);
      toast.error('Failed to mark payment as received. Please try again.');
    }
  };

  const getStatusBadgeClasses = (status: string): string => {
    const statusClasses = {
      Accepted: 'bg-[#00A912] text-white',
      Pending: 'bg-[#f09e22] text-white',
      Completed: 'bg-[#4caf50] text-white',
      Declined: 'bg-[#EB001B] text-white',
      Cancelled: 'bg-[#6B7280] text-white',
    };

    return `px-3 py-1 rounded-full text-sm font-semibold ${statusClasses[status] || 'bg-gray-100 text-gray-800'}`;
  };

  const canExpand = (booking: Booking): boolean => {
    return booking.booking_status === 'Completed';
  };

  const handleBookingError = (error: unknown) => {
    let errorMessage = 'Failed to update booking status';

    if (error && typeof error === 'object') {
      const errorObj = error as { status?: string; data?: { error?: string } };
      if (errorObj.status === 'FETCH_ERROR') {
        errorMessage =
          'Network error. Please check your connection and try again.';
      } else if (errorObj.data?.error) {
        errorMessage = errorObj.data.error;
      }
    }

    toast.error(errorMessage);
  };

  return {
    declineForm,
    openDeclineForm,
    closeDeclineForm,
    updateDeclineReason,
    submitDecline,
    acceptBooking,
    declineBooking,
    completeBooking,
    startService,
    endService,
    markPaymentReceived,
    getStatusBadgeClasses,
    canExpand,
  };
};
