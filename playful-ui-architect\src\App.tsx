import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from './store/store';
import Index from './pages/Index';
import Login from './pages/Login';
import SignUp from './pages/SignUp';
import Onboarding from './pages/Onboarding';
import ForgotPassword from './pages/ForgotPassword.tsx';
import VerifyOTP from './pages/VerifyOTP.tsx';
import ResetPassword from './pages/ResetPassword.tsx';
import NotFound from './pages/NotFound';
import OTPVerification from './pages/OtpVerification';
import Home from './pages/Home';
import LocationSelection from './pages/LocationSelection';
import Profile from './pages/Profile.tsx';
import ProfileDetails from './pages/ProfileDetails';
import PrivacyPolicy from './pages/Privacy-policy';
import Feedback from './pages/Feedback';
import { FeedbackLinkGenerator } from '@/components/FeedbackLinkGenerator';
import { FeedbackForm } from '@/components/FeedbackForm';
import AvailabilityNew from './pages/AvailabilityNew.tsx';
import Documents from './pages/Documents.tsx';
import Settings from './pages/Settings.tsx';
import NotificationSettings from './components/NotificationSettings.tsx';
import PasswordManager from './components/PasswordManager.tsx';
import DeleteAccount from './components/DeleteAccount.tsx';
import BankAccount from './components/profile/BankAccount.tsx';
import Animation from './components/Animation.tsx';
import Schedule from './pages/Schedule.tsx';
import Chat from './pages/Chat.tsx';
import ChatModal from './components/ChatModal.tsx';
import ReferNurse from './pages/ReferNurse.tsx';
const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Animation>
            <Routes>
              <Route path='/' element={<Index />} />
              <Route path='/login' element={<Login />} />
              <Route path='/signup' element={<SignUp />} />
              <Route path='/verify-otp' element={<OTPVerification />} />
              <Route path='/onboarding' element={<Onboarding />} />
              <Route path='/forgotpassword' element={<ForgotPassword />} />
              <Route path='/otp-verify' element={<VerifyOTP />} />
              <Route path='/reset-password' element={<ResetPassword />} />
              <Route path='/home' element={<Home />} />
              <Route path='/location' element={<LocationSelection />} />
              <Route path='/profile' element={<Profile />} />
              {}
              <Route path='/availability' element={<AvailabilityNew />} />
              <Route path='/profile-details' element={<ProfileDetails />} />
              <Route path='/privacy-policy' element={<PrivacyPolicy />} />
              <Route path='/feedback' element={<Feedback />} />
              <Route
                path='/feedback/generate'
                element={<FeedbackLinkGenerator />}
              />
              <Route path='/feedback/:feedbackId' element={<FeedbackForm />} />
              <Route path='/documents' element={<Documents />} />
              <Route path='/settings' element={<Settings />} />
              <Route path='/notifications' element={<NotificationSettings />} />
              <Route path='/password-manager' element={<PasswordManager />} />
              <Route path='/delete-account' element={<DeleteAccount />} />
              <Route path='/bank-accounts' element={<BankAccount />} />
              <Route path='/schedule' element={<Schedule />} />
              <Route path='/chat' element={<Chat />} />
              <Route path='/refer-nurse' element={<ReferNurse />} />
              <Route
                path='/chat/:conversationId'
                element={<ChatModal mode='fullscreen' />}
              />
              <Route path='*' element={<NotFound />} />
            </Routes>
          </Animation>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </Provider>
);

export default App;
