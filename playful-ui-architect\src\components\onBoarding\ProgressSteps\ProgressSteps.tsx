import React from 'react';
import { CheckCircle2 } from 'lucide-react';

interface ProgressStepsProps {
  step: number;
}

const ProgressSteps = ({ step }: ProgressStepsProps) => {
  return (
    <div className='flex items-center justify-between mb-6'>
      <div className='flex flex-col items-center w-1/3'>
        <div
          className={`h-6 w-6 rounded-full ${step >= 1 ? 'bg-nursery-blue' : 'bg-gray-300'} flex items-center justify-center`}
        >
          {step > 1 && <CheckCircle2 className='h-4 w-4 text-white' />}
        </div>
        <span className='text-sm text-center mt-2'>Personal Details</span>
      </div>
      <div
        className={`h-1 flex-1 ${step >= 2 ? 'bg-nursery-blue' : 'bg-gray-200'} mx-2`}
      ></div>
      <div className='flex flex-col items-center w-1/3'>
        <div
          className={`h-6 w-6 rounded-full ${step >= 2 ? 'bg-nursery-blue' : 'bg-gray-300'} mb-2 flex items-center justify-center`}
        >
          {step > 2 && <CheckCircle2 className='h-4 w-4 text-white' />}
        </div>
        <span className='text-sm text-center'>Professional Details</span>
      </div>
      <div
        className={`h-1 flex-1 ${step >= 3 ? 'bg-nursery-blue' : 'bg-gray-200'} mx-2`}
      ></div>
      <div className='flex flex-col items-center w-1/3'>
        <div
          className={`h-6 w-6 rounded-full ${step === 3 ? 'bg-nursery-blue' : 'bg-gray-300'} mb-2`}
        ></div>
        <span className='text-sm text-center'>Upload Documents</span>
      </div>
    </div>
  );
};

export default ProgressSteps;
