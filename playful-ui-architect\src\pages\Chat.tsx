import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, User } from 'lucide-react';
import {
  useGetBookingsByNurseQuery,
  Booking,
} from '@/store/api/customerApiSlice';
import ResponsiveLoader from '@/components/Loader';
import ChatModal from '@/components/ChatModal';
import Footer from '@/components/Footer';

const Chat = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<{
    customer_cognitoId: string;
    customer_given_name: string;
  } | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);

  const userId = localStorage.getItem('userId');
  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
  } = useGetBookingsByNurseQuery(userId, {
    skip: !userId,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const bookingsData = bookingResponse?.bookings || [];

  const uniqueCustomersMap = new Map();
  bookingsData.forEach((booking: Booking) => {
    if (
      (booking.booking_status === 'Accepted' ||
        booking.booking_status === 'Completed') &&
      !uniqueCustomersMap.has(booking.customer_cognitoId)
    ) {
      uniqueCustomersMap.set(booking.customer_cognitoId, booking);
    }
  });
  const uniqueCustomers = Array.from(uniqueCustomersMap.values());

  const displayedCustomers = uniqueCustomers.filter((booking: Booking) =>
    booking.customer_given_name
      ?.toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-8 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed '>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        <div className=' absolute z-10 flex items-center top-5 '>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Chat</h1>
        </div>
      </header>

      {}
      <main className='flex-1'>
        {}
        <div className='mt-5 w-11/12 md:w-5/12 mx-auto'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5' />
            <input
              type='text'
              placeholder='Search by patient name'
              className='w-full pl-10 pr-4 py-3 border-gray-300 bg-[#F2F2F2] rounded-md text-sm text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nursery-blue'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className='w-11/12 md:w-10/12 mx-auto mt-5 space-y-4'>
          {bookingLoading && <ResponsiveLoader />}
          {bookingError && (
            <div className='text-center text-red-500 py-8'>
              Error loading bookings.
            </div>
          )}
          {!bookingLoading && !bookingError && bookingsData.length === 0 && (
            <div className='text-center text-gray-500 py-8'>
              No bookings found.
            </div>
          )}
          <p className='md:text-lg text-md text-nursery-darkBlue font-semibold '>
            Chat with Your patient
          </p>
          {displayedCustomers.map((booking: Booking) => (
            <button
              key={booking.customer_cognitoId}
              className='flex w-full items-center bg-[#F2F2F2] gap-3 md:p-3 p-2 border-b rounded-lg shadow-lg hover:bg-gray-100 transition-colors'
              onClick={() => {
                setSelectedPatient({
                  customer_cognitoId: booking.customer_cognitoId,
                  customer_given_name: booking.customer_given_name,
                });
                setIsChatOpen(true);
              }}
            >
              <div className='w-9 h-9 md:w-10 md:h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
                <User className='w-5 h-5 md:w-6 md:h-6 text-white' />
              </div>
              <span className='font-semibold text-lg'>
                {booking.customer_given_name}
              </span>
            </button>
          ))}
        </div>
      </main>
      <Footer />
      <ChatModal
        selectedPatient={selectedPatient}
        isOpen={isChatOpen}
        onClose={() => {
          setIsChatOpen(false);
          setSelectedPatient(null);
        }}
      />
    </div>
  );
};

export default Chat;
