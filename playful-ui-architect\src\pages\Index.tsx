import React, { useEffect, useRef } from 'react';
import Nurse<PERSON><PERSON> from '@/components/NurseHero';
import LoginButton from '@/components/LoginButton';
import SignUpButton from '@/components/SignUpButton';

const Index = () => {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-slide-up');
            entry.target.classList.remove('opacity-0');
          }
        });
      },
      { threshold: 0.1 }
    );

    const children = contentRef.current?.children;
    if (children) {
      Array.from(children).forEach(child => {
        observer.observe(child);
      });
    }

    return () => {
      if (children) {
        Array.from(children).forEach(child => {
          observer.unobserve(child);
        });
      }
    };
  }, []);

  return (
    <div className='flex flex-col h-screen justify-center items-center relative w-full overflow-hidden'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          loading='lazy'
          src='/Images/bg4.png'
          alt=''
          className='object-cover w-full h-full'
        />
      </div>
      <NurseHero />

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
      <div className='relative items-center z-20 -mt-16 md:-mt-16 px-6 w-full max-w-md mx-auto'>
        <div
          ref={contentRef}
          className='rounded-lg bg-white py-6 px-6 flex flex-col gap-6'
        >
          <h2
            className='text-2xl font-semibold text-center text-slate-900 opacity-0'
            style={{ animationDelay: '0.2s' }}
          >
            Register as a Nearby Nurse
          </h2>

          <p
            className=' text-base text-center text-gray-500 opacity-0'
            style={{ animationDelay: '0.3s' }}
          >
            Flexible work hours, verified
            <br />
            opportunities, and secure payments.
          </p>

          <div
            className='space-y-4 opacity-0'
            style={{ animationDelay: '0.4s' }}
          >
            <LoginButton />
            <SignUpButton />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
