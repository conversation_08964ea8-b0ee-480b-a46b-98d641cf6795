import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const customerBaseUrl = 'https://testcustomerapi.nurserv.com/api';

export interface Booking {
  id: number;
  booking_id: number;
  nurse_cognitoId: string;
  customer_cognitoId: string;
  nurse_given_name: string;
  customer_given_name: string;
  nurse_location_latitude: string;
  nurse_location_longitude: string;
  nurse_location_address: string;
  customer_booked_location_address: string;
  customer_booked_location_latitude: string;
  customer_booked_location_longitude: string;
  hourly_fare: string;
  booked_date: string;
  booked_slot: string;
  booking_status: string;
  created_at: string;
  updated_at: string;
  services_selected: string | null;
  cancellation_reason?: string;
}

export interface GetBookingsByNurseResponse {
  total_bookings: number;
  bookings: Booking[];
}

export interface UpdateBookingStatusRequest {
  booking_id: number;
  booking_status:
    | 'Pending'
    | 'Accepted'
    | 'Declined'
    | 'Cancelled'
    | 'Completed';
  custom_reason?: string;
}

export interface UpdateBookingStatusResponse {
  message: string;
  booking_id: number;
  booking_status: string;
}

export interface ServiceStatus {
  id: number;
  booking_id: number;
  status: 'not_started' | 'started' | 'completed' | 'payment_received';
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  payment_received_at?: string;

  nurse_cognitoId?: string;
  customer_cognitoId?: string;
  customer_given_name?: string;
  nurse_given_name?: string;
  booked_date?: string;
  booked_slot?: string;
  booking_status?: string;
  hourly_fare?: string;
  services_selected?: string;
}

export interface ServiceStatusResponse {
  success: boolean;
  message: string;
  data: ServiceStatus | ServiceStatus[] | null;
}

export interface UpdateServiceStatusRequest {
  booking_id: number;
  status: 'not_started' | 'started' | 'completed' | 'payment_received';
}

export interface PopulateAcceptedBookingsResponse {
  success: boolean;
  message: string;
  data: {
    affectedRows: number;
  };
}

export interface CancellationReason {
  id: number;
  reason: string;
  description?: string;
}

export const customerApiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: customerBaseUrl,

    prepareHeaders: (headers, { getState: _getState }) => {
      const token = localStorage.getItem('idToken');

      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Bookings', 'ServiceStatus'],
  endpoints: builder => ({
    getBookingsByNurse: builder.query<GetBookingsByNurseResponse, string>({
      query: nurse_cognitoId => ({
        url: `/bookings/nurse/${nurse_cognitoId}`,
        method: 'GET',
      }),
      providesTags: ['Bookings'],
    }),

    updateBookingStatus: builder.mutation<
      UpdateBookingStatusResponse,
      UpdateBookingStatusRequest
    >({
      query: ({ booking_id, booking_status, custom_reason }) => {
        const body: { booking_status: string; custom_reason?: string } = {
          booking_status,
        };

        if (custom_reason !== undefined && custom_reason.trim() !== '') {
          body.custom_reason = custom_reason;
        }

        return {
          url: `/bookings/${booking_id}/status`,
          method: 'PUT',
          body,
        };
      },
      invalidatesTags: ['Bookings', 'ServiceStatus'],
    }),

    getBookingById: builder.query<{ booking: Booking }, number>({
      query: booking_id => ({
        url: `/bookings/${booking_id}`,
        method: 'GET',
      }),
      providesTags: ['Bookings'],
    }),

    getServiceStatusByBookingId: builder.query<ServiceStatusResponse, number>({
      query: bookingId => ({
        url: `/service-status/booking/${bookingId}`,
        method: 'GET',
      }),
      providesTags: (result, error, bookingId) => [
        { type: 'ServiceStatus', id: bookingId },
        'ServiceStatus',
      ],
    }),

    updateServiceStatus: builder.mutation<
      ServiceStatusResponse,
      UpdateServiceStatusRequest
    >({
      query: ({ booking_id, status }) => ({
        url: `/service-status/booking/${booking_id}`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { booking_id }) => [
        { type: 'ServiceStatus', id: booking_id },
        'ServiceStatus',
      ],
    }),

    getNurseServiceStatuses: builder.query<ServiceStatusResponse, string>({
      query: nurseCognitoId => ({
        url: `/service-status/nurse/${nurseCognitoId}`,
        method: 'GET',
      }),
      providesTags: (result, error, nurseCognitoId) => [
        { type: 'ServiceStatus', id: `nurse-${nurseCognitoId}` },
        'ServiceStatus',
      ],
    }),

    getCustomerServiceStatuses: builder.query<ServiceStatusResponse, string>({
      query: customerCognitoId => ({
        url: `/service-status/customer/${customerCognitoId}`,
        method: 'GET',
      }),
      providesTags: (result, error, customerCognitoId) => [
        { type: 'ServiceStatus', id: `customer-${customerCognitoId}` },
        'ServiceStatus',
      ],
    }),

    populateAcceptedBookings: builder.mutation<
      PopulateAcceptedBookingsResponse,
      void
    >({
      query: () => ({
        url: `/service-status/populate-accepted`,
        method: 'POST',
      }),
      invalidatesTags: ['ServiceStatus'],
    }),

    getCancellationReasons: builder.query<CancellationReason[], void>({
      query: () => '/cancellation-reasons',
      transformResponse: (response: { data?: CancellationReason[] }) => {
        return response?.data || [];
      },
    }),
  }),
});

export const {
  useGetBookingsByNurseQuery,
  useUpdateBookingStatusMutation,
  useGetBookingByIdQuery,

  useGetServiceStatusByBookingIdQuery,
  useUpdateServiceStatusMutation,
  useGetNurseServiceStatusesQuery,
  useGetCustomerServiceStatusesQuery,
  usePopulateAcceptedBookingsMutation,

  useGetCancellationReasonsQuery,
} = customerApiSlice;
