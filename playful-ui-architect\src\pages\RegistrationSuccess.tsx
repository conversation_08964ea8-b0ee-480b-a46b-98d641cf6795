import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';

interface RegistrationSuccessProps {
  open: boolean;
  onClose: () => void;
}

const RegistrationSuccess = ({ open, onClose }: RegistrationSuccessProps) => {
  const navigate = useNavigate();

  const handleContactSupport = () => {
    navigate('/location');
  };

  const handleClose = () => {
    onClose();

    navigate('/location');
  };

  return (
    <Dialog open={open} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className='sm:max-w-md p-3 gap-0'>
        <DialogHeader className='p-6 pt-8 pb-0 space-y-0'>
          <div className='sr-only'>
            <DialogTitle>Registration Successful</DialogTitle>
            <DialogDescription>Your profile is under review</DialogDescription>
          </div>
        </DialogHeader>

        <div className='px-6 pb-6 flex flex-col items-center'>
          <div className='bg-green-500 rounded-full p-4 mb-4'>
            <CheckCircle className='h-10 w-10 text-white' />
          </div>

          <h2 className='text-xl font-bold text-center mb-4'>
            Registered Successfully
          </h2>

          <p className='text-center text-gray-700 mb-2'>
            Your profile and documents are currently under review. Our
            verification team is processing your submission, and this may take
            approximately
            <span className='font-semibold'> 24-48 hours</span>.
          </p>

          <p className='text-center text-gray-700 mb-6'>
            You will receive a notification once your profile is approved. If
            any additional information is needed, we will reach out to you.
          </p>

          <p className='text-center text-gray-700 mb-8'>
            For any queries, feel free to contact our support team
          </p>

          <Button
            className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white mb-4'
            onClick={handleContactSupport}
          >
            Contact Support
          </Button>

          <Button
            variant='outline'
            className='w-full h-12 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
            onClick={handleClose}
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RegistrationSuccess;
