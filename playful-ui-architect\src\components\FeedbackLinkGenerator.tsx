import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/utils/toast';
import Logo from './onBoarding/Logo/Logo';

export function FeedbackLinkGenerator() {
  const [searchParams] = useSearchParams();
  const recipientName = searchParams.get('recipientName');
  const nurseId = searchParams.get('nurseId');
  const [feedbackLink, setFeedbackLink] = useState('');

  const generateFeedbackLink = () => {
    if (!recipientName.trim()) {
      toast.error('Please enter a recipient name');
      return;
    }

    if (!nurseId) {
      toast.error('Nurse ID is missing');
      return;
    }

    const uniqueId = crypto.randomUUID();

    const link = `${window.location.origin}/feedback/${uniqueId}?recipient=${encodeURIComponent(recipientName)}&nurseId=${encodeURIComponent(nurseId)}`;
    setFeedbackLink(link);

    const feedbackSessions = JSON.parse(
      localStorage.getItem('feedbackSessions') || '{}'
    );
    feedbackSessions[uniqueId] = {
      recipientName,
      nurseId,
      createdAt: new Date().toISOString(),
      status: 'pending',
    };
    localStorage.setItem('feedbackSessions', JSON.stringify(feedbackSessions));
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(feedbackLink);
    toast.success('Feedback link copied to clipboard');
  };

  return (
    <div className='relative w-full min-h-screen flex flex-col gap-2 justify-center items-center p-4'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='../../public/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
      <Logo />
      <Card className='max-w-md w-full mx-3 relative z-10'>
        <CardHeader>
          <CardTitle>Generate Feedback Link</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4 '>
            <div className='space-y-2'>
              <Label htmlFor='recipientName'>Recipient Name</Label>
              <Input
                id='recipientName'
                value={recipientName}
                placeholder="Enter recipient's name"
                disabled={!!recipientName}
              />
            </div>

            <div className='space-y-2'>
              <Label>Nurse ID</Label>
              <Input value={nurseId || ''} disabled className='bg-gray-100' />
            </div>
            <Button onClick={generateFeedbackLink} className='w-full'>
              Generate Link
            </Button>

            {feedbackLink && (
              <div className='space-y-2'>
                <Label>Feedback Link</Label>
                <div className='flex gap-2'>
                  <Input value={feedbackLink} readOnly className='flex-1' />
                  <Button variant='outline' onClick={copyToClipboard}>
                    Copy
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
