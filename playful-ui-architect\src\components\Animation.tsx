import React, { useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import ResponsiveLoader from './Loader';

interface AnimationProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

const pageVariants = {
  initial: { opacity: 0, y: 10 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -10 },
};

const pageTransition = { duration: 0.28, ease: 'easeOut' };

const Animation: React.FC<AnimationProps> = ({
  children,
  isLoading = false,
}) => {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <div className='fixed inset-0 w-full h-full overflow-auto bg-gray-200'>
      <AnimatePresence mode='wait' initial={false}>
        {!isLoading && (
          <motion.div
            key={location.pathname}
            initial='initial'
            animate='in'
            exit='out'
            variants={pageVariants}
            transition={pageTransition}
            className='min-h-screen w-full'
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>

      {}
      {isLoading && <ResponsiveLoader />}
    </div>
  );
};

export default Animation;
