import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff, Check, X } from 'lucide-react';
import { toast } from '@/utils/toast';
import { z } from 'zod';
import { useConfirmForgotPasswordMutation } from '@/store/api/apiSlice';

const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .regex(
        /[^A-Za-z0-9]/,
        'Password must contain at least one special character'
      )
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type RTKError = {
  status: number | string;
  data?: { error?: string; message?: string };
};

const isRTKError = (err: unknown): err is RTKError => {
  return typeof err === 'object' && err !== null && 'status' in err;
};

const handleResetPasswordError = (error: unknown) => {
  console.error('Password reset error:', error);

  if (
    isRTKError(error) &&
    (error.status === 404 || error.data?.error === 'User not found')
  ) {
    toast.error('User not found. Please check your phone number.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Invalid confirmation code'
  ) {
    toast.error('Invalid OTP. Please verify your OTP and try again.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Invalid confirmation code or password format'
  ) {
    toast.error('Invalid OTP or password format. Please check and try again.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error ===
      'Phone number, confirmation code, and new password are required'
  ) {
    toast.error('Missing required information. Please try again.');
  } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
    toast.error('Network error. Please check your connection and try again.');
  } else if (
    isRTKError(error) &&
    (error.status === 500 || error.data?.error === 'Failed to reset password')
  ) {
    toast.error('Failed to reset password. Please try again later.');
  } else if (isRTKError(error) && error.data?.message) {
    toast.error(error.data.message);
  } else {
    toast.error('Password reset failed. Please try again.');
  }
};

interface PasswordInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  showPassword: boolean;
  onToggleVisibility: () => void;
  error?: string;
  placeholder?: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  label,
  value,
  onChange,
  showPassword,
  onToggleVisibility,
  error,
  placeholder = '',
}) => (
  <div className='relative'>
    <p className='text-sm text-gray-800 mb-1'>{label}</p>
    <div className='relative'>
      <Input
        type={showPassword ? 'text' : 'password'}
        value={value}
        onChange={e => onChange(e.target.value)}
        className='w-full h-11 text-base pr-10'
        placeholder={placeholder}
        required
      />
      <button
        type='button'
        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600'
        onClick={onToggleVisibility}
      >
        {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
      </button>
    </div>
    {error && <p className='text-red-500 text-sm mt-1'>{error}</p>}
  </div>
);

const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetPassword, { isLoading }] = useConfirmForgotPasswordMutation();
  const [errors, setErrors] = useState<{
    newPassword?: string;
    confirmPassword?: string;
  }>({});

  const phone_number = location.state?.phone_number;
  const confirmationCode = location.state?.confirmationCode;

  const handleInputChange = (field: string, value: string) => {
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    switch (field) {
      case 'newPassword':
        setNewPassword(value);
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    const formData = {
      newPassword,
      confirmPassword,
    };
    const validationResult = passwordSchema.safeParse(formData);

    if (!validationResult.success) {
      const fieldErrors = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });
      setErrors(fieldErrors);
      return;
    }

    try {
      const response = await resetPassword({
        phone_number,
        confirmationCode,
        newPassword,
      }).unwrap();

      if (response) {
        setTimeout(() => {
          toast.success('Your password has been reset successfully.');
          navigate('/login');
        }, 1500);
      }
    } catch (error: unknown) {
      handleResetPasswordError(error);
    }
  };

  return (
    <div className='relative h-screen w-full overflow-hidden'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-30' />

      <div className='relative z-20 flex-1 flex flex-col items-center justify-center px-4 pt-10 md:pt-20'>
        <img
          src='/Images/Logo.svg'
          alt='Nurses team'
          className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mb-2'
        />

        {}
        <div className='w-full max-w-md bg-white rounded-xl p-4 shadow-lg'>
          <h2 className='text-2xl font-bold text-nursery-darkBlue mb-6'>
            Password Recovery
          </h2>

          <form onSubmit={handleSubmit} noValidate className='space-y-6'>
            <PasswordInput
              label='New Password'
              value={newPassword}
              onChange={value => handleInputChange('newPassword', value)}
              showPassword={showNewPassword}
              onToggleVisibility={() => setShowNewPassword(!showNewPassword)}
              error={errors.newPassword}
            />

            <div>
              <PasswordInput
                label='Re-type Password'
                value={confirmPassword}
                onChange={value => handleInputChange('confirmPassword', value)}
                showPassword={showConfirmPassword}
                onToggleVisibility={() =>
                  setShowConfirmPassword(!showConfirmPassword)
                }
                error={errors.confirmPassword}
              />

              {newPassword && confirmPassword && (
                <div className='flex items-center text-xs mt-2'>
                  {newPassword === confirmPassword ? (
                    <>
                      <Check className='h-3 w-3 text-green-500 mr-1' />
                      <span className='text-green-700'>Passwords match</span>
                    </>
                  ) : (
                    <>
                      <X className='h-3 w-3 text-red-500 mr-1' />
                      <span className='text-red-700'>
                        Passwords don&apos;t match
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>

            <Button
              type='submit'
              className='w-full h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg font-medium'
              disabled={isLoading}
            >
              {isLoading ? 'Resetting Password...' : 'Reset Password'}
            </Button>
          </form>
        </div>

        {}
        <div className='mt-6 w-full max-w-md p-4 bg-white bg-opacity-90 rounded-lg border border-gray-200'>
          <h3 className='text-sm font-medium text-gray-800 mb-2'>
            Security Tips:
          </h3>
          <ul className='text-xs text-gray-700 space-y-1'>
            <li>• Use a unique password that you don&apos;t use elsewhere</li>
            <li>• Consider using a password manager for all your accounts</li>
            <li>• Change your password regularly</li>
            <li>• Never share your password with anyone</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
