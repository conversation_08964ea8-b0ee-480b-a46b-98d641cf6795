const { pool } = require("../config/database");

class ServiceStatus {
  // Get service status by booking ID
  static async getByBookingId(bookingId) {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM service_status WHERE booking_id = ?',
        [bookingId]
      );
      return rows[0] || null;
    } catch (error) {
      console.error('Error in getByBookingId:', error);
      throw new Error(`Error fetching service status: ${error.message}`);
    }
  }

  // Create new service status record (only for accepted bookings)
  static async create(bookingId, status = 'not_started') {
    try {
      // First verify the booking exists and is accepted
      const [bookingRows] = await pool.execute(
        'SELECT booking_id FROM bookings WHERE booking_id = ? AND booking_status = ?',
        [bookingId, 'Accepted']
      );
      
      if (bookingRows.length === 0) {
        throw new Error('Booking not found or not accepted');
      }

      const [result] = await pool.execute(
        'INSERT INTO service_status (booking_id, status, created_at, updated_at) VALUES (?, ?, CONVERT_TZ(NOW(), "UTC", "+05:30"), CONVERT_TZ(NOW(), "UTC", "+05:30"))',
        [bookingId, status]
      );
      return result.insertId;
    } catch (error) {
      console.error('Error in create:', error);
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Service status already exists for this booking');
      }
      throw new Error(`Error creating service status: ${error.message}`);
    }
  }

  // Update service status with timestamp
  static async updateStatus(bookingId, status) {
    try {
      const timestampField = ServiceStatus.getTimestampField(status);
      let query = 'UPDATE service_status SET status = ?, updated_at = CONVERT_TZ(NOW(), "UTC", "+05:30")';
      let params = [status];

      if (timestampField) {
        query += `, ${timestampField} = CONVERT_TZ(NOW(), "UTC", "+05:30")`;
      }

      query += ' WHERE booking_id = ?';
      params.push(bookingId);

      const [result] = await pool.execute(query, params);

      if (result.affectedRows === 0) {
        throw new Error('Service status not found');
      }

      return result.affectedRows;
    } catch (error) {
      console.error('Error in updateStatus:', error);
      throw new Error(`Error updating service status: ${error.message}`);
    }
  }

  // Get all service statuses for a nurse (only accepted bookings)
  static async getAllByNurseId(nurseId) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          ss.*, 
          b.nurse_cognitoId, 
          b.customer_given_name, 
          b.booked_date, 
          b.booked_slot,
          b.booking_status,
          b.hourly_fare,
          b.services_selected
        FROM service_status ss
        JOIN bookings b ON ss.booking_id = b.booking_id
        WHERE b.nurse_cognitoId = ? AND (b.booking_status = 'Accepted' || b.booking_status = 'Completed')
        ORDER BY ss.updated_at DESC
      `, [nurseId]);
      return rows;
    } catch (error) {
      console.error('Error in getAllByNurseId:', error);
      throw new Error(`Error fetching nurse service statuses: ${error.message}`);
    }
  }

  // Get all service statuses for a customer (only accepted bookings)
  static async getAllByCustomerId(customerId) {
    try {
      const [rows] = await pool.execute(`
        SELECT 
          ss.*, 
          b.customer_cognitoId, 
          b.nurse_cognitoId, 
          b.customer_given_name, 
          b.nurse_given_name,
          b.booked_date, 
          b.booked_slot,
          b.booking_status,
          b.hourly_fare,
          b.services_selected
        FROM service_status ss
        JOIN bookings b ON ss.booking_id = b.booking_id
        WHERE b.customer_cognitoId = ? AND (b.booking_status = 'Accepted' || b.booking_status = 'Completed')
        ORDER BY ss.updated_at DESC
      `, [customerId]);
      return rows;
    } catch (error) {
      console.error('Error in getAllByCustomerId:', error);
      throw new Error(`Error fetching customer service statuses: ${error.message}`);
    }
  }

  // Fixed method name (was populateAcceptepoolookings)
  static async populateAcceptedBookings() {
    try {
      const [result] = await pool.execute(`
        INSERT INTO service_status (booking_id, status, created_at, updated_at)
        SELECT booking_id, 'not_started', CONVERT_TZ(NOW(), "UTC", "+05:30"), CONVERT_TZ(NOW(), "UTC", "+05:30")
        FROM bookings 
        WHERE booking_status = 'Accepted' 
        AND booking_id NOT IN (SELECT booking_id FROM service_status)
      `);
      return result.affectedRows;
    } catch (error) {
      console.error('Error in populateAcceptedBookings:', error);
      throw new Error(`Error populating service status: ${error.message}`);
    }
  }

  // Helper method to get timestamp field based on status
  static getTimestampField(status) {
    switch (status) {
      case 'started':
        return 'started_at';
      case 'completed':
        return 'completed_at';
      case 'payment_received':
        return 'payment_received_at';
      default:
        return null;
    }
  }
}

module.exports = ServiceStatus;