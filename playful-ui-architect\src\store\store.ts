import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import { customerApiSlice } from './api/customerApiSlice';
import { chatApiSlice } from './api/chatApiSlice';

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    [customerApiSlice.reducerPath]: customerApiSlice.reducer,
    [chatApiSlice.reducerPath]: chatApiSlice.reducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware()
      .concat(apiSlice.middleware)
      .concat(customerApiSlice.middleware)
      .concat(chatApiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
