export type NullableString = string | null;
export type NullableNumber = number | null;
export type NullableFile = File | null;
export type NullableDate = Date | null;

export type FileUploadState = {
  file: NullableFile;
  previewUrl: NullableString;
  isUploading: boolean;
  error: NullableString;
};

export type FormFieldState = 'idle' | 'loading' | 'success' | 'error';
export type UploadStatus = 'pending' | 'uploading' | 'completed' | 'failed';

export type ApiResponse<T = unknown> = {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
};

export type DocumentUploadResponse = ApiResponse<{
  url: string;
  filename: string;
  size: number;
  type: string;
}>;
