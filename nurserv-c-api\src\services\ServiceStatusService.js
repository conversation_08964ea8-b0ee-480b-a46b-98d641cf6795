const ServiceStatus = require('../models/ServiceStatus');

class ServiceStatusService {
  // Get service status by booking ID with authorization check
  static async getService(bookingId, userId) {
    try {
      if (!bookingId) {
        throw new Error('Booking ID is required');
      }

      // Verify user has access to this booking
      if (userId) {
        await this.verifyUserAccess(bookingId, userId);
      }

      const serviceStatus = await ServiceStatus.getByBookingId(bookingId);

      if (!serviceStatus) {
        throw new Error('Service status not found. Booking may not be accepted yet.');
      }

      return serviceStatus;
    } catch (error) {
      console.error('Error in getService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Update service status - only nurses can update
  static async updateService(bookingId, status, userId) {
    try {
      if (!bookingId || !status) {
        throw new Error('Booking ID and status are required');
      }

      if (!userId) {
        throw new Error('User ID is required for authorization');
      }

      const validStatuses = ['not_started', 'started', 'completed', 'payment_received'];
      if (!validStatuses.includes(status)) {
        throw new Error('Invalid status provided');
      }

      // Verify user has permission to update this booking (must be the assigned nurse)
      await this.verifyNursePermission(bookingId, userId);

      // Get current status
      const currentStatus = await ServiceStatus.getByBookingId(bookingId);
      if (!currentStatus) {
        // If service status doesn't exist, create it first
        await ServiceStatus.create(bookingId, 'not_started');
        const newCurrentStatus = await ServiceStatus.getByBookingId(bookingId);
        if (!newCurrentStatus) {
          throw new Error('Failed to create service status record');
        }
      }

      // Get the latest current status after potential creation
      const latestStatus = await ServiceStatus.getByBookingId(bookingId);

      // Validate status transition
      this.validateStatusTransition(latestStatus.status, status);

      // Update service status
      await ServiceStatus.updateStatus(bookingId, status);
      const updatedStatus = await ServiceStatus.getByBookingId(bookingId);

      return {
        success: true,
        message: 'Service status updated successfully',
        data: updatedStatus
      };
    } catch (error) {
      console.error('Error in updateService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Get all service statuses for a nurse
  static async getNurseService(nurseId) {
    try {
      if (!nurseId) {
        throw new Error('Nurse ID is required');
      }

      const serviceStatuses = await ServiceStatus.getAllByNurseId(nurseId);
      return serviceStatuses;
    } catch (error) {
      console.error('Error in getNurseService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Get all service statuses for a customer
  static async getCustomerService(customerId) {
    try {
      if (!customerId) {
        throw new Error('Customer ID is required');
      }

      const serviceStatuses = await ServiceStatus.getAllByCustomerId(customerId);
      return serviceStatuses;
    } catch (error) {
      console.error('Error in getCustomerService:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Populate service status for accepted bookings (admin function)
  static async populateAcceptedBookings() {
    try {
      const affectedRows = await ServiceStatus.populateAcceptedBookings();
      return {
        success: true,
        message: `${affectedRows} service status records created`,
        data: { affectedRows }
      };
    } catch (error) {
      console.error('Error in populateAcceptedBookings:', error);
      throw new Error(`Service error: ${error.message}`);
    }
  }

  // Verify user has access to view booking (both nurse and customer)
  static async verifyUserAccess(bookingId, userId) {
    try {
      const { pool } = require("../config/database");

      if (!userId) {
        throw new Error('User ID is required for access verification');
      }

      const [rows] = await pool.execute(
        'SELECT booking_id FROM bookings WHERE booking_id = ? AND (nurse_cognitoId = ? OR customer_cognitoId = ?)',
        [bookingId, userId, userId]
      );

      if (rows.length === 0) {
        throw new Error('Unauthorized: You do not have permission to view this booking');
      }
    } catch (error) {
      console.error('Error in verifyUserAccess:', error);
      throw error;
    }
  }

  // Verify nurse has permission to update booking (only assigned nurse)
  static async verifyNursePermission(bookingId, nurseId) {
    try {
      const { pool } = require("../config/database");

      if (!nurseId) {
        throw new Error('Nurse ID is required for permission verification');
      }

      const [rows] = await pool.execute(
        'SELECT booking_id FROM bookings WHERE booking_id = ? AND nurse_cognitoId = ?',
        [bookingId, nurseId]
      );

      if (rows.length === 0) {
        throw new Error('Unauthorized: You do not have permission to update this booking');
      }
    } catch (error) {
      console.error('Error in verifyNursePermission:', error);
      throw error;
    }
  }

  // Validate status transition
  static validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      'not_started': ['started'],
      'started': ['completed'],
      'completed': ['payment_received'],
      'payment_received': [] // No further transitions allowed
    };

    if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
      throw new Error(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}

module.exports = ServiceStatusService;