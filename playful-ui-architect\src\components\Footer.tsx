import { Calendar, HomeIcon, MessageSquare, User } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useNavigationState } from '@/hooks/useNavigationState';
import NotificationBadge from '@/components/NotificationBadge';
import { useUnreadMessages } from '@/hooks/useUnreadMessages';
import { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { useGetCurrentUserProfileImageQuery } from '@/store/api/apiSlice';

const Footer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { navigationState } = useNavigationState();
  const { unreadCount, refetch } = useUnreadMessages();
  const { data: currentProfileImage } = useGetCurrentUserProfileImageQuery();
  const profileImage = currentProfileImage?.profile_image?.signed_url;

  useEffect(() => {
    refetch();
  }, [refetch]);

  return (
    <footer className=' flex justify-around py-3 bg-white fixed bottom-0 md:hidden transition-all duration-500 z-50 w-full '>
      <button
        type='button'
        className={`flex flex-col items-center cursor-pointer transition-colors border-none bg-transparent p-2 ${location.pathname === '/home' ? 'text-nursery-blue' : 'text-gray-500'}`}
        onClick={() => navigate('/home', { state: navigationState })}
        aria-label='Navigate to Home'
      >
        <HomeIcon className='h-6 w-6' />
        <span className='text-xs mt-1'>Home</span>
      </button>
      <button
        type='button'
        className={`flex flex-col items-center cursor-pointer transition-colors border-none bg-transparent p-2 ${location.pathname === '/schedule' ? 'text-nursery-blue' : 'text-gray-500'}`}
        onClick={() => navigate('/schedule')}
        aria-label='Navigate to Bookings'
      >
        <Calendar className='h-6 w-6' />
        <span className='text-xs mt-1'>Bookings</span>
      </button>
      <button
        type='button'
        className={`flex flex-col items-center cursor-pointer transition-colors border-none bg-transparent p-2 ${location.pathname === '/chat' ? 'text-nursery-blue' : 'text-gray-500'}`}
        onClick={() => navigate('/chat')}
        aria-label={
          unreadCount > 0
            ? `Navigate to Chat (${unreadCount} unread messages)`
            : 'Navigate to Chat'
        }
      >
        <div className='relative'>
          <MessageSquare className='h-6 w-6' />
          <NotificationBadge count={unreadCount} />
        </div>
        <span className='text-xs mt-1'>Chat</span>
      </button>
      <button
        type='button'
        className={`flex flex-col items-center cursor-pointer transition-colors border-none bg-transparent p-2 ${location.pathname === '/profile' ? 'text-nursery-blue' : 'text-gray-500'}`}
        onClick={() => navigate('/profile')}
        aria-label='Navigate to Profile'
      >
        <Avatar
          className={`h-8 w-8 p-[2px] shadow-sm ${profileImage ? 'bg-nursery-darkBlue' : ''}`}
        >
          {' '}
          {profileImage ? (
            <AvatarImage
              src={profileImage}
              alt='Profile'
              className='object-cover rounded-full'
            />
          ) : (
            <AvatarFallback className='bg-white'>
              <User className='h-6 w-6' />
            </AvatarFallback>
          )}
        </Avatar>
        <span className='text-xs mt-1'>Profile</span>
      </button>
    </footer>
  );
};

export default Footer;
