import React, { useState, useEffect } from 'react';
import { Search, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetBookingsByNurseQuery,
  useUpdateBookingStatusMutation,
  useGetNurseServiceStatusesQuery,
  useUpdateServiceStatusMutation,
} from '../store/api/customerApiSlice';
import ResponsiveLoader from '@/components/Loader';
import BookingCard from '../components/BookingCard';
import Footer from '@/components/Footer';
import { toast } from '@/utils/toast';
import calender from '/Images/calender.svg';

function Schedule() {
  const navigate = useNavigate();
  const userId = localStorage.getItem('userId');

  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
  } = useGetBookingsByNurseQuery(userId, {
    skip: !userId,
    pollingInterval: 60000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: serviceStatusResponse,
    isLoading: _serviceStatusLoading,
    error: serviceStatusError,
  } = useGetNurseServiceStatusesQuery(userId, {
    skip: !userId,
    pollingInterval: 30000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const [updateBookingStatus, { isLoading: updatingStatus }] =
    useUpdateBookingStatusMutation();
  const [updateServiceStatus, { isLoading: updatingServiceStatus }] =
    useUpdateServiceStatusMutation();

  const [selectedTab, setSelectedTab] = useState('Pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [serviceStatus, setServiceStatus] = useState({});
  const [expandedBookings, setExpandedBookings] = useState(new Set());

  useEffect(() => {
    if (bookingError) {
      handleApiError(bookingError);
    }
  }, [bookingError]);

  useEffect(() => {
    if (serviceStatusResponse?.success && serviceStatusResponse?.data) {
      const statusMap = {};
      serviceStatusResponse.data.forEach(status => {
        statusMap[status.booking_id] = status;
      });
      setServiceStatus(statusMap);
    }
  }, [serviceStatusResponse]);

  const handleApiError = error => {
    console.error('API Error:', error);

    const errorMessage = error?.data?.error || error?.message;
    const statusCode = error?.status;

    const errorMessages = {
      401: 'Session expired. Please log in again.',
      403: 'Access denied. Please check your permissions.',
      404: 'Bookings not found. Please try refreshing.',
      500: 'Server error. Please try again later.',
    };

    let message = errorMessages[statusCode];

    if (!message) {
      if (error?.name === 'NetworkError' || !navigator.onLine) {
        message = 'Network error. Please check your connection.';
      } else {
        message = errorMessage || 'Failed to load bookings. Please try again.';
      }
    }

    toast.error(message);
  };

  const handleBookingStatusUpdate = async (
    bookingId,
    status,
    customReason = null
  ) => {
    try {
      await updateBookingStatus({
        booking_id: bookingId,
        booking_status: status,
        ...(customReason && { custom_reason: customReason }),
      }).unwrap();

      const statusMessages = {
        Accepted: 'Booking accepted successfully!',
        Declined: 'Booking declined successfully!',
        Completed: 'Booking marked as completed!',
      };

      toast.success(statusMessages[status]);

      if (status === 'Completed') {
        setSelectedTab('Complete');
      }
    } catch (error) {
      console.error('Failed to update booking status:', error);

      let errorMessage = 'Failed to update booking status';
      if (error?.status === 'FETCH_ERROR') {
        errorMessage =
          'Network error. Please check your connection and try again.';
      } else if (error?.data?.error) {
        errorMessage = error.data.error;
      }

      toast.error(errorMessage);
    }
  };

  const handleServiceStatusUpdate = async (
    bookingId,
    status,
    successMessage
  ) => {
    try {
      const response = await updateServiceStatus({
        booking_id: bookingId,
        status: status,
      }).unwrap();

      setServiceStatus(prev => ({
        ...prev,
        [bookingId]: {
          ...prev[bookingId],
          status: status,
          [`${status}_at`]:
            response.data[`${status}_at`] || new Date().toISOString(),
        },
      }));

      if (status === 'completed') {
        await handleBookingStatusUpdate(bookingId, 'Completed');
      }

      toast.success(successMessage);
    } catch (error) {
      console.error(`Failed to ${status} service:`, error);

      toast.error(`Failed to ${status} service. Please try again.`);
    }
  };

  const getStatusForTab = tab => {
    const statusMap = {
      Pending: ['Pending'],
      Upcoming: ['Accepted'],
      Complete: ['Completed'],
      Cancelled: ['Cancelled', 'Declined'],
    };
    return statusMap[tab] || [];
  };

  const getFilteredAndSortedBookings = () => {
    const filteredBookings =
      bookingResponse?.bookings?.filter(booking =>
        getStatusForTab(selectedTab).includes(booking.booking_status)
      ) || [];

    const sortedBookings = [...filteredBookings].sort((a, b) => {
      const statusA = serviceStatus[a.booking_id]?.status || 'not_started';
      const statusB = serviceStatus[b.booking_id]?.status || 'not_started';

      if (
        (statusA === 'completed' || statusA === 'payment_received') &&
        (statusB === 'completed' || statusB === 'payment_received')
      ) {
        if (statusA === 'completed' && statusB === 'payment_received')
          return -1;
        if (statusA === 'payment_received' && statusB === 'completed') return 1;
      }

      return (
        new Date(b.booked_date).getTime() - new Date(a.booked_date).getTime()
      );
    });

    return sortedBookings.filter(booking =>
      booking.customer_given_name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase())
    );
  };

  const displayedBookings = getFilteredAndSortedBookings();

  const renderHeader = () => (
    <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
      <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='object-cover w-full'
        />
      </div>
      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
      <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1'>
        <button onClick={() => navigate(-1)} className='mr-3'>
          <ArrowLeft className='h-6 w-6' />
        </button>
        <h1 className='text-xl font-semibold'>Bookings</h1>
      </div>
    </header>
  );

  const renderTabNavigation = () => (
    <div className='flex md:w-6/12 w-full mx-auto bg-[#F2F2F2] rounded-lg p-1 md:mb-5 mb-4 mt-5'>
      {['Pending', 'Upcoming', 'Complete', 'Cancelled'].map(tab => (
        <button
          key={tab}
          className={`flex-1 py-2 px-2 text-sm font-medium ${
            selectedTab === tab
              ? 'bg-nursery-blue rounded-md text-white'
              : 'text-gray-600'
          }`}
          onClick={() => setSelectedTab(tab)}
        >
          {tab === 'Complete' ? 'Completed' : tab}
        </button>
      ))}
    </div>
  );

  const renderSearchBar = () => (
    <div className='md:mb-6 mb-4'>
      <div className='relative md:w-6/12 w-full mx-auto'>
        <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5' />
        <input
          type='text'
          placeholder='Search by patient name'
          className='w-full pl-10 pr-4 py-3 border-gray-300 bg-[#F2F2F2] rounded-md text-sm text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-nursery-blue'
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
        />
      </div>
    </div>
  );

  const renderBookingsList = () => {
    if (bookingLoading) {
      return (
        <div className='bg-slate-100 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <ResponsiveLoader />
        </div>
      );
    }

    if (bookingError || serviceStatusError) {
      return (
        <div className='bg-red-50 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <p className='text-base font-medium text-red-500'>
            Failed to load schedules {serviceStatusError && 'or service status'}
          </p>
          <p className='text-sm text-red-400'>Please try again later</p>
        </div>
      );
    }

    if (displayedBookings.length === 0) {
      return (
        <div className='bg-slate-100 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2 md:mb-0 mb-20'>
          <img src={calender} alt='NO Schedules for you' className='p-2 w-20' />
          <p className='text-base font-medium text-slate-500'>
            No {selectedTab.toLowerCase()} bookings for you
          </p>
        </div>
      );
    }

    return (
      <div className='space-y-4 md:mb-0 mb-20'>
        {displayedBookings.map((booking, index) => (
          <BookingCard
            key={booking.id || index}
            booking={booking}
            index={index}
            serviceStatus={serviceStatus}
            setServiceStatus={setServiceStatus}
            onUpdateBookingStatus={handleBookingStatusUpdate}
            onUpdateServiceStatus={handleServiceStatusUpdate}
            updatingStatus={updatingStatus}
            updatingServiceStatus={updatingServiceStatus}
            expandedBookings={expandedBookings}
            setExpandedBookings={setExpandedBookings}
            selectedTab={selectedTab}
            setSelectedTab={setSelectedTab}
          />
        ))}
      </div>
    );
  };
  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {renderHeader()}

      <div className='w-11/12 md:w-10/12 mx-auto min-h-screen'>
        {renderTabNavigation()}
        {renderSearchBar()}

        <div className='md:pb-8 pb-4'>{renderBookingsList()}</div>
      </div>
      <Footer />
    </div>
  );
}
export default Schedule;
