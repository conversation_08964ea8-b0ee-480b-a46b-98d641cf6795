import React, { useState, useRef, useEffect } from 'react';
import { toast } from '@/utils/toast';
import {
  ArrowLeft,
  FileText,
  Download,
  Trash2,
  Plus,
  Upload,
  X,
} from 'lucide-react';
import {
  useListDocumentsQuery,
  useUploadDocumentMutation,
  useDeleteDocumentMutation,
  useGetDocumentUrlQuery,
  Document,
} from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { useNavigate } from 'react-router-dom';

interface DocumentsProps {
  onBack?: () => void;
}

const Documents: React.FC<DocumentsProps> = ({ onBack: _onBack }) => {
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [description, setDescription] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [downloadingDocumentKey, setDownloadingDocumentKey] = useState<
    string | null
  >(null);

  const {
    data: documentsData,
    isLoading,
    error: listError,
    refetch,
  } = useListDocumentsQuery();
  const [uploadDocument, { isLoading: isUploading }] =
    useUploadDocumentMutation();
  const [deleteDocument] = useDeleteDocumentMutation();

  const {
    data: documentUrlData,
    isLoading: isLoadingUrl,
    error: urlError,
  } = useGetDocumentUrlQuery(downloadingDocumentKey!, {
    skip: !downloadingDocumentKey,
  });

  const navigate = useNavigate();

  useEffect(() => {
    if (listError) {
      console.error('Failed to load documents:', listError);

      if (
        listError?.status === 500 ||
        listError?.data?.error === 'Failed to list documents'
      ) {
        toast.error('Failed to load documents. Please try again later.', {
          style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
          duration: 3000,
          dismissible: true,
        });
      } else if (listError?.status === 'FETCH_ERROR') {
        toast.error(
          'Network error loading documents. Please check your connection.',
          {
            style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
            duration: 3000,
            dismissible: true,
          }
        );
      } else {
        toast.error('Failed to load documents. Please refresh the page.', {
          style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
          duration: 3000,
          dismissible: true,
        });
      }
    }
  }, [listError]);

  useEffect(() => {
    if (documentUrlData?.url && downloadingDocumentKey) {
      try {
        const link = document.createElement('a');
        link.href = documentUrlData.url;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success('Download started successfully!', {
          style: { backgroundColor: '#FFFFFF', color: '#16A34A' },
          duration: 2000,
          dismissible: true,
        });
      } catch (error) {
        console.error('Download failed:', error);
        toast.error('Failed to download document. Please try again.', {
          style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
          duration: 3000,
          dismissible: true,
        });
      } finally {
        setDownloadingDocumentKey(null);
      }
    }
  }, [documentUrlData, downloadingDocumentKey]);

  useEffect(() => {
    if (urlError && downloadingDocumentKey) {
      console.error('Failed to get download URL:', urlError);
      toast.error('Failed to prepare download. Please try again.', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      setDownloadingDocumentKey(null);
    }
  }, [urlError, downloadingDocumentKey]);

  const groupedDocuments = React.useMemo(() => {
    if (!documentsData?.documents) return {};
    return documentsData.documents.reduce(
      (acc, doc) => {
        const type = doc.document_type || 'Other Documents';
        if (!acc[type]) acc[type] = [];
        acc[type].push(doc);
        return acc;
      },
      {} as Record<string, Document[]>
    );
  }, [documentsData]);

  const getStatusColor = () => {
    return 'bg-orange-100 text-orange-900';
  };

  const getStatusText = () => {
    return 'Under Review';
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !selectedCategory) {
      toast.error('Please select a file and category before uploading.');
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('document_type', selectedCategory);
    formData.append('description', description);
    formData.append('is_public', 'false');

    try {
      await uploadDocument(formData).unwrap();
      toast.success('Document uploaded successfully!');
      setUploadModalOpen(false);
      setSelectedFile(null);
      setDescription('');
      setSelectedCategory('');
      refetch();
    } catch (error) {
      console.error('Upload failed:', error);

      if (error?.status === 400 && error?.data?.error === 'No file uploaded') {
        toast.error('No file selected for upload. Please choose a file.');
      } else if (
        error?.status === 400 &&
        error?.data?.error?.includes('Invalid document_type')
      ) {
        toast.error(
          'Invalid document category. Please select a valid document type.'
        );
      } else if (
        error?.status === 500 &&
        error?.data?.error?.includes('Failed to upload document')
      ) {
        toast.error('Failed to upload document. Please try again later.');
      } else if (error?.status === 'FETCH_ERROR') {
        toast.error(
          'Network error during upload. Please check your connection.'
        );
      } else {
        toast.error('Failed to upload document. Please try again.');
      }
    }
  };

  const handleDeleteClick = (s3Key: string) => {
    setDocumentToDelete(s3Key);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!documentToDelete) return;

    try {
      await deleteDocument(documentToDelete).unwrap();
      toast.success('Document deleted successfully');
      refetch();
    } catch (error: unknown) {
      console.error('Delete failed:', error);
      const apiError = error as { data?: { error?: string } };
      toast.error(apiError.data?.error || 'Failed to delete document');
    } finally {
      setDeleteModalOpen(false);
      setDocumentToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setDocumentToDelete(null);
  };

  const handleDownload = (doc: Document) => {
    if (downloadingDocumentKey) {
      toast.error('Another download is in progress. Please wait.', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      return;
    }

    if (!doc.s3_key) {
      toast.error('Document key not found. Cannot download.', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      return;
    }

    setDownloadingDocumentKey(doc.s3_key);
    toast.info('Preparing download...');
  };

  const DocumentSection = ({
    title,
    documents,
    showAddButton = true,
  }: {
    title: string;
    documents: Document[];
    showAddButton?: boolean;
  }) => (
    <div className='mb-8'>
      <div className='flex items-center justify-between mb-4'>
        <h2 className='text-lg font-semibold text-gray-800'>{title}</h2>
        {showAddButton && (
          <button
            onClick={() => {
              setSelectedCategory(title);
              setUploadModalOpen(true);
            }}
            className='flex items-center gap-2 text-nursery-darkBlue hover:text-nursery-navy font-medium'
          >
            <Plus size={16} />
            Add Files
          </button>
        )}
      </div>

      <div className='space-y-3'>
        {documents.map(doc => {
          return (
            <div
              key={doc.id}
              className='flex md:flex-row flex-col gap-3 justify-between p-4 bg-white rounded-lg border border-gray-200'
            >
              <div className='flex items-center gap-3'>
                <div className='p-2 bg-red-50 rounded-lg'>
                  <FileText className='w-5 h-5 text-red-600' />
                </div>
                <div>
                  <h3 className='font-medium text-gray-900'>
                    {doc.original_name}
                  </h3>
                  <span
                    className={`inline-block mt-1 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}
                  >
                    {getStatusText()}
                  </span>
                </div>
              </div>

              <div className='flex items-center gap-2'>
                <button
                  onClick={() => handleDownload(doc)}
                  disabled={
                    downloadingDocumentKey === doc.s3_key || isLoadingUrl
                  }
                  className='p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                  title={
                    downloadingDocumentKey === doc.s3_key
                      ? 'Downloading...'
                      : 'Download'
                  }
                >
                  <Download size={16} />
                </button>
                <button
                  onClick={() => handleDeleteClick(doc.s3_key)}
                  className='p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors'
                  title='Delete'
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          );
        })}

        {documents.length === 0 && (
          <div className='text-center py-8 text-gray-500'>
            No documents uploaded yet
          </div>
        )}
      </div>
    </div>
  );

  if (isLoading) {
    return <ResponsiveLoader />;
  }

  return (
    <div className='min-h-screen bg-[#F2F2F2]'>
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Documents</h1>
        </div>
      </header>

      <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        <DocumentSection
          title='ID Proof'
          documents={groupedDocuments['ID Proof'] || []}
        />
        <DocumentSection
          title='Experience Proof'
          documents={groupedDocuments['Experience Proof'] || []}
        />
        <DocumentSection
          title='Other Documents'
          documents={groupedDocuments['Other Documents'] || []}
        />

        {Object.entries(groupedDocuments)
          .filter(
            ([type]) =>
              !['ID Proof', 'Experience Proof', 'Other Documents'].includes(
                type
              )
          )
          .map(([type, documents]) => (
            <DocumentSection key={type} title={type} documents={documents} />
          ))}
      </div>

      {uploadModalOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg max-w-md w-full p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h2 className='text-lg font-semibold'>Upload Document</h2>
              <button
                onClick={() => setUploadModalOpen(false)}
                className='p-2 hover:bg-gray-100 rounded-lg'
              >
                <X size={20} />
              </button>
            </div>

            <div className='space-y-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Document Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={e => setSelectedCategory(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
                >
                  <option value=''>Select category</option>
                  <option value='ID Proof'>ID Proof</option>
                  <option value='Experience Proof'>Experience Proof</option>
                  <option value='Other Documents'>Other Documents</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Description (Optional)
                </label>
                <input
                  type='text'
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  placeholder='Brief description of the document'
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nursery-blue focus:border-transparent'
                />
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Select File
                </label>
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className='border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-nursery-blue transition-colors'
                >
                  {selectedFile ? (
                    <div>
                      <FileText className='w-8 h-8 text-nursery-blue mx-auto mb-2' />
                      <p className='text-sm font-medium text-gray-900'>
                        {selectedFile.name}
                      </p>
                      <p className='text-xs text-gray-500'>
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  ) : (
                    <div>
                      <Upload className='w-8 h-8 text-gray-400 mx-auto mb-2' />
                      <p className='text-sm text-gray-600'>
                        Click to select file
                      </p>
                      <p className='text-xs text-gray-500'>Max size: 10MB</p>
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type='file'
                  onChange={handleFileSelect}
                  className='hidden'
                  accept='.pdf,.doc,.docx,.jpg,.jpeg,.png'
                />
              </div>
            </div>

            <div className='flex gap-3 mt-6'>
              <button
                onClick={() => setUploadModalOpen(false)}
                className='flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors'
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={!selectedFile || !selectedCategory || isUploading}
                className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-lg hover:bg-nursery-darkBlue disabled:bg-gray-300 disabled:cursor-not-allowed duration-200 transition-colors'
              >
                {isUploading ? 'Uploading...' : 'Upload'}
              </button>
            </div>
          </div>
        </div>
      )}

      {}
      {deleteModalOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-55 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-xl p-6 w-full max-w-sm mx-4 shadow-xl'>
            <div className='flex justify-between items-center mb-4'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Confirm Delete
              </h3>
              <button
                onClick={handleCancelDelete}
                className='p-1 rounded-full transition-colors'
              >
                <X className='h-5 w-5 text-gray-500 hover:text-red-600 hover:scale-110 transition-transform duration-200' />
              </button>
            </div>

            <p className='text-gray-700 mb-6'>
              Are you sure you want to delete this document?
            </p>

            <div className='flex gap-3'>
              <button
                onClick={handleCancelDelete}
                className='flex-1 px-4 py-2 bg-[#F2F2F2] border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-300 hover:text-gray-800 transition-colors font-medium'
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-lg hover:bg-nursery-darkBlue transition-colors duration-200 font-medium shadow-lg'
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Documents;
