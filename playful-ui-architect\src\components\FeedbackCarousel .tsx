import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star, User } from 'lucide-react';
import Stars from '../../public/Images/stars.svg';

const FEEDBACKS_PER_VIEW = 1;
const STAR_RATING_MAX = 5;
const DEFAULT_RATING = 0;

const CSS_CLASSES = {
  CONTAINER_BG: 'bg-[#F2F2F2]',
  BUTTON_PRIMARY: 'bg-nursery-blue hover:bg-nursery-darkBlue',
  BUTTON_DISABLED: 'bg-gray-400 cursor-not-allowed',
  TEXT_PRIMARY: 'text-gray-800',
  TEXT_SECONDARY: 'text-gray-700',
  TEXT_MUTED: 'text-gray-500',
} as const;

interface FeedbackItem {
  id?: string | number;
  name?: string;
  rating?: number;
  comments?: string;
  created_at?: string;
}

interface FeedbackCarouselProps {
  feedbacks?: FeedbackItem[];
}

const FeedbackCarousel: React.FC<FeedbackCarouselProps> = ({
  feedbacks = [],
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const totalPages = Math.ceil(feedbacks.length / FEEDBACKS_PER_VIEW);

  const getCurrentFeedbacks = () => {
    const start = currentIndex * FEEDBACKS_PER_VIEW;
    const end = start + FEEDBACKS_PER_VIEW;
    return feedbacks.slice(start, end);
  };

  const goToPrevious = () => {
    setCurrentIndex(prev => (prev > 0 ? prev - 1 : totalPages - 1));
  };

  const goToNext = () => {
    setCurrentIndex(prev => (prev < totalPages - 1 ? prev + 1 : 0));
  };

  const isFirstFeedback = currentIndex === 0;
  const isLastFeedback = currentIndex === totalPages - 1;

  const renderStarRating = (rating: number) => {
    const safeRating = rating || DEFAULT_RATING;
    return (
      <div className='flex items-center gap-1'>
        {Array.from({ length: STAR_RATING_MAX }, (_, index) => index + 1).map(
          star => (
            <Star
              key={`star-${star}`}
              className={`w-4 h-4 ${
                star <= safeRating
                  ? 'text-[#f09e22] fill-current'
                  : 'text-gray-400'
              }`}
            />
          )
        )}
        <span className='ml-1 text-sm font-medium text-gray-700'>
          {safeRating}/{STAR_RATING_MAX}
        </span>
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    if (!dateString || typeof dateString !== 'string') {
      return 'Date unavailable';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return 'Date unavailable';
    }
  };

  const renderPaginationDots = () => (
    <div
      className='flex justify-center items-center mx-auto mt-4 gap-2'
      role='tablist'
      aria-label='Feedback pagination'
    >
      {Array.from({ length: totalPages }, (_, index) => (
        <button
          key={`pagination-page-${index}-of-${totalPages}`}
          onClick={() => setCurrentIndex(index)}
          className={`w-2 h-2 rounded-full transition-all duration-200 ${
            index === currentIndex
              ? 'bg-nursery-darkBlue w-6'
              : 'bg-gray-600 hover:bg-gray-400'
          }`}
          role='tab'
          aria-selected={index === currentIndex}
          aria-label={`Go to page ${index + 1} of ${totalPages}`}
          tabIndex={index === currentIndex ? 0 : -1}
        />
      ))}
    </div>
  );

  if (feedbacks.length === 0) {
    return (
      <div className='bg-[#F2F2F2] p-7 md:p-7 md:mb-0 mb-20 items-center flex flex-1 flex-col justify-start rounded-xl gap-2'>
        <img
          src={Stars}
          alt='Currently NO Feedbacks Available'
          className='p-2 w-20'
        />
        <p className='text-base font-medium text-slate-500'>
          No Previous Feedbacks for you
        </p>
        <p className='text-sm text-slate-400 text-center'>
          Your amazing feedback will appear here once you start getting reviews!
        </p>
      </div>
    );
  }

  return (
    <div className='relative mb-20 md:mb-0'>
      {}

      <div className='bg-[#F2F2F2] rounded-xl p-4 min-h-[280px] relative overflow-hidden'>
        {totalPages > 1 && (
          <>
            <button
              onClick={goToPrevious}
              disabled={isFirstFeedback}
              className={`absolute md:left-6 left-5 top-1/2 transform -translate-y-1/2 z-10 rounded-full p-2 shadow-md transition-all duration-300 ${
                isFirstFeedback
                  ? CSS_CLASSES.BUTTON_DISABLED
                  : `${CSS_CLASSES.BUTTON_PRIMARY} hover:shadow-lg`
              }`}
              aria-label='Previous feedbacks'
            >
              <ChevronLeft className='w-5 h-5 text-white' />
            </button>

            <button
              onClick={goToNext}
              disabled={isLastFeedback}
              className={`absolute md:right-6 right-5 top-1/2 transform -translate-y-1/2 z-10 rounded-full p-2 shadow-md transition-all duration-300 ${
                isLastFeedback
                  ? CSS_CLASSES.BUTTON_DISABLED
                  : `${CSS_CLASSES.BUTTON_PRIMARY} hover:shadow-lg`
              }`}
              aria-label='Next feedbacks'
            >
              <ChevronRight className='w-5 h-5 text-white' />
            </button>
          </>
        )}

        {}
        <div className='flex justify-center items-center w-full'>
          <div className='h-full w-full'>
            {getCurrentFeedbacks().map((feedback, index) => {
              const feedbackKey = feedback?.id
                ? `feedback-${feedback.id}`
                : `feedback-page-${currentIndex}-item-${index}`;

              return (
                <div
                  key={feedbackKey}
                  className='bg-[#F2F2F2] p-4 px-14 md:p-8 md:px-14 w-full rounded-lg  flex flex-col justify-between min-h-[220px] shadow-xl'
                >
                  {}
                  <div className='flex flex-col justify-between items-start mb-3 '>
                    <div className='flex flex-row items-center gap-2 mb-2 w-full'>
                      <div className='bg-nursery-blue p-1.5 rounded-full'>
                        <User size={20} className='text-white' />
                      </div>
                      <h3 className='font-semibold text-gray-800 flex-1'>
                        {feedback?.name || 'Anonymous'}
                      </h3>
                    </div>
                    <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2'>
                      <div>{renderStarRating(feedback?.rating || 0)}</div>
                      <p className='text-xs text-gray-500'>
                        {formatDate(feedback?.created_at || '')}
                      </p>
                    </div>
                  </div>

                  {}
                  <div className='flex-1'>
                    <p className='text-gray-700 text-sm leading-relaxed'>
                      {feedback?.comments || 'No comments provided'}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {}
        <div className='flex justify-center'>
          {feedbacks.length > 0 && (
            <div className='mt-3 text-sm font-semibold text-nursery-darkBlue'>
              <span>
                Showing {currentIndex * FEEDBACKS_PER_VIEW + 1} of{' '}
                {feedbacks.length} feedbacks
              </span>
            </div>
          )}
        </div>
        {totalPages > 1 && renderPaginationDots()}
      </div>
    </div>
  );
};

export default FeedbackCarousel;
