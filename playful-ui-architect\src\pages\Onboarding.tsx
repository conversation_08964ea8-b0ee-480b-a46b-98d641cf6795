import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Logo from '../components/onBoarding/Logo/Logo';
import ProgressSteps from '../components/onBoarding/ProgressSteps/ProgressSteps';
import PersonalDetailsForm from '../components/onBoarding/PersonalDetailsForm/PersonalDetailsForm';
import ProfessionalDetailsForm from '../components/onBoarding/ProfessionalDetailsForm/ProfessionalDetailsForm';
import DocumentUploadForm from '../components/onBoarding/DocumentUploadForm/DocumentUploadForm';
import RegistrationSuccess from './RegistrationSuccess';
import {
  useCreatePersonalDetailsMutation,
  useUploadDocumentMutation,
  useGetProfileQuery,
} from '@/store/api/apiSlice';
import { toast } from '@/utils/toast';
import { z } from 'zod';

import ResponsiveLoader from '@/components/Loader';

const _onboardingSchema = z.object({
  date_of_birth: z.date({ required_error: 'Date of birth is required' }),
  gender: z.string().min(1, 'Gender is required'),
  emergency_contact: z.string().min(10, 'Emergency contact is required'),
  service_provide: z
    .array(z.string())
    .min(1, 'At least one specialization is required'),
  nuid: z.string().min(6, 'NUID is required').max(12, 'Enter valid NUID'),
  about: z.string().min(15, 'About is required, enter atleast 15 chars'),
  total_years_of_exp: z.number().min(1, 'Experience is required'),
  selected_services: z
    .array(z.string())
    .min(1, 'At least one service must be selected'),
  custom_service: z.string().optional(),
});

const LOCAL_STORAGE_KEY = 'onboarding_form_data';

const Onboarding = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    date_of_birth: undefined,
    gender: '',
    emergency_contact: '',
    total_years_of_exp: 0,
    selected_services: [],
    service_provide: [],
    custom_service: '',
    idProofFiles: [],
    experienceProofFiles: [],
    otherDocumentFiles: [],
    nuid: '',
    about: '',
    nurse_onboard_complete: false,
  });
  const [fieldErrors, setFieldErrors] = useState({});

  const [createPersonalDetails, { isLoading: _isCreatingDetails }] =
    useCreatePersonalDetailsMutation();
  const [uploadDocument] = useUploadDocumentMutation();
  const { data: profileData, isLoading: isLoadingProfile } = useGetProfileQuery(
    undefined,
    {
      skip: !localStorage.getItem('idToken') && !localStorage.getItem('token'),
    }
  );

  useEffect(() => {
    const savedData = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        if (parsedData.date_of_birth) {
          parsedData.date_of_birth = new Date(parsedData.date_of_birth);
        }

        const initializedData = {
          ...parsedData,
          idProofFiles: [],
          experienceProofFiles: [],
          otherDocumentFiles: [],

          service_provide: Array.isArray(parsedData.service_provide)
            ? parsedData.service_provide
            : parsedData.service_provide
              ? [parsedData.service_provide]
              : [],
        };
        setFormData(initializedData);
      } catch (error) {
        console.error('Failed to parse saved form data:', error);
      }
    }

    const token =
      localStorage.getItem('idToken') || localStorage.getItem('token');
    if (!token) {
      toast.warning('Please login to continue with onboarding');
      navigate('/login');
    }
  }, [navigate]);

  const updateFormData = newData => {
    const updatedData = { ...formData, ...newData };
    setFormData(updatedData);

    try {
      const dataToStore = { ...updatedData };

      if (dataToStore.date_of_birth instanceof Date) {
        dataToStore.date_of_birth = dataToStore.date_of_birth.toISOString();
      }

      ['idProofFiles', 'experienceProofFiles', 'otherDocumentFiles'].forEach(
        fileType => {
          if (updatedData[fileType]?.length) {
            const metadata = updatedData[fileType].map(file => ({
              name: file.name,
              size: file.size,
              type: file.type,
              lastModified: file.lastModified,
            }));
            dataToStore[`${fileType}Metadata`] = JSON.stringify(metadata);
          }
        }
      );

      delete dataToStore.idProofFiles;
      delete dataToStore.experienceProofFiles;
      delete dataToStore.otherDocumentFiles;

      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Failed to save form data to localStorage:', error);
    }
  };

  const handleNext = () => {
    if (step === 1) {
      try {
        const personalSchema = z.object({
          date_of_birth: z.date({
            required_error: 'Date of birth is required',
          }),
          gender: z.string().min(1, 'Gender is required'),
          emergency_contact: z
            .string()
            .min(10, 'Emergency contact is required'),
        });

        personalSchema.parse(formData);
        setFieldErrors({});
        setStep(2);
      } catch (error) {
        if (error instanceof z.ZodError) {
          setFieldErrors(error.flatten().fieldErrors);
          toast.error('Please fill all required fields');
        }
      }
    } else if (step === 2) {
      try {
        const professionalSchema = z.object({
          service_provide: z
            .array(z.string())
            .min(1, 'At least one specialization is required'),
          total_years_of_exp: z.number().min(1, 'Experience is required'),
          selected_services: z
            .array(z.string())
            .min(1, 'At least one service must be selected'),
          nuid: z
            .string()
            .min(6, 'NUID is required')
            .max(12, 'Enter valid NUID'),
          about: z
            .string()
            .min(15, 'About is required, enter atleast 15 chars'),
        });

        professionalSchema.parse(formData);
        setFieldErrors({});
        setStep(3);
      } catch (error) {
        if (error instanceof z.ZodError) {
          setFieldErrors(error.flatten().fieldErrors);
          toast.error('Please fill all required fields');
        }
      }
    }
  };

  const handleApiSubmit = async () => {
    try {
      const token =
        localStorage.getItem('idToken') || localStorage.getItem('token');
      if (!token) {
        toast.error('You are not logged in. Please login to continue.');
        navigate('/login');
        return;
      }

      const apiFormData = {
        ...formData,
        date_of_birth:
          formData.date_of_birth instanceof Date
            ? formData.date_of_birth.toLocaleDateString('en-CA')
            : formData.date_of_birth,
        nurse_onboard_complete: true,

        service_provide: Array.isArray(formData.service_provide)
          ? formData.service_provide
          : [formData.service_provide].filter(Boolean),
      };

      const personalDetailsResponse =
        await createPersonalDetails(apiFormData).unwrap();

      if (
        personalDetailsResponse.message ===
        'Personal details created successfully'
      ) {
        toast.success('Profile updated successfully');

        localStorage.removeItem(LOCAL_STORAGE_KEY);
        setShowSuccessDialog(true);
        navigate('/location', { state: { nurse_onboarding_completed: true } });
      } else {
        toast.error(
          personalDetailsResponse.message || 'Failed to update profile'
        );
      }
    } catch (error) {
      console.error('API Error:', error);

      if (
        error?.status === 409 ||
        error?.data?.error === 'Personal details already exist for this user'
      ) {
        toast.error(
          'Personal details already exist. Please contact support if you need to update them.'
        );
      } else if (error?.status === 401) {
        toast.error('Authentication failed. Please login again.');
        localStorage.removeItem('idToken');
        localStorage.removeItem('token');
        navigate('/login');
      } else if (error?.status === 400) {
        toast.error(
          error?.data?.message ||
            'Invalid data provided. Please check your inputs.'
        );
      } else if (
        error?.status === 500 ||
        error?.data?.error === 'Failed to create personal details'
      ) {
        toast.error(
          'Failed to create personal details. Please try again later.'
        );
      } else if (error?.status === 'FETCH_ERROR') {
        toast.error(
          'Network error. Please check your connection and try again.'
        );
      } else {
        toast.error(
          error?.data?.message ||
            'An error occurred while updating your profile'
        );
      }
    }
  };

  const handleCancel = () => {
    navigate('/');
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const uploadAllDocuments = async () => {
    const token =
      localStorage.getItem('idToken') || localStorage.getItem('token');
    if (!token) {
      toast.error('You are not logged in. Please login to continue.');
      return false;
    }

    setUploading(true);
    let hasErrors = false;

    const uploadFileGroup = async (files, documentType) => {
      for (const file of files) {
        const formDataForUpload = new FormData();
        formDataForUpload.append('file', file);
        formDataForUpload.append('document_type', documentType);

        try {
          await uploadDocument(formDataForUpload).unwrap();
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);

          if (
            error?.status === 400 &&
            error?.data?.error === 'No file uploaded'
          ) {
            toast.error(`No file selected for upload: ${file.name}`);
          } else if (
            error?.status === 400 &&
            error?.data?.error?.includes('Invalid document_type')
          ) {
            toast.error(
              'Invalid document type. Please select a valid document category.'
            );
          } else if (
            error?.status === 500 &&
            error?.data?.error?.includes('Failed to upload document')
          ) {
            toast.error(
              `Failed to upload ${file.name}. Please try again later.`
            );
          } else if (error?.status === 'FETCH_ERROR') {
            toast.error(
              `Network error while uploading ${file.name}. Please check your connection.`
            );
          } else {
            toast.error(
              `Error uploading ${file.name}: ${error?.data?.message || 'Upload failed'}`
            );
          }
          hasErrors = true;
        }
      }
    };

    try {
      if (formData.idProofFiles.length > 0) {
        await uploadFileGroup(formData.idProofFiles, 'ID Proof');
      }

      if (formData.experienceProofFiles.length > 0) {
        await uploadFileGroup(
          formData.experienceProofFiles,
          'Experience Proof'
        );
      }

      if (formData.otherDocumentFiles.length > 0) {
        await uploadFileGroup(formData.otherDocumentFiles, 'Other Documents');
      }

      setUploading(false);
      return !hasErrors;
    } catch (error) {
      console.error('Failed to upload all documents:', error);
      setUploading(false);
      toast.error('Failed to upload documents. Please try again.');
      return false;
    }
  };

  const handleFormSubmit = async () => {
    try {
      const uploadSuccess = await uploadAllDocuments();

      if (uploadSuccess) {
        toast.success('All documents uploaded successfully!');
        await handleApiSubmit();
      } else {
        toast.error(
          'There was an issue uploading some documents. Please try again.'
        );
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('An unexpected error occurred during submission.');
    }
  };

  const handleCloseSuccessDialog = () => {
    setShowSuccessDialog(false);
    navigate('/location', { state: { nurse_onboarding_completed: true } });
  };

  if (isLoadingProfile) {
    return <ResponsiveLoader />;
  }

  return (
    <div className='relative w-full overflow-hidden min-h-screen flex flex-col pl-4 pr-4 pt-4'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt=''
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

      <div className='relative z-10 h-full w-full flex-1 flex flex-col items-center justify-center pt-2'>
        <div className='mb-1 scale-75'>
          <Logo />
        </div>

        <div className='w-full max-w-md bg-white p-5 rounded-xl'>
          <h1 className='text-2xl font-semibold text-nursery-blue mb-1'>
            Welcome!
          </h1>
          {profileData?.data?.given_name && (
            <h2 className='text-2xl font-medium text-nursery-darkBlue mb-6'>
              {profileData.data.given_name}
            </h2>
          )}

          <ProgressSteps step={step} />

          {step === 1 && (
            <PersonalDetailsForm
              formData={formData}
              updateFormData={updateFormData}
              fieldErrors={fieldErrors}
              handleNext={handleNext}
              handleCancel={handleCancel}
            />
          )}

          {step === 2 && (
            <ProfessionalDetailsForm
              formData={formData}
              updateFormData={updateFormData}
              fieldErrors={fieldErrors}
              handleNext={handleNext}
              handleBack={handleBack}
            />
          )}

          {step === 3 && (
            <DocumentUploadForm
              formData={formData}
              updateFormData={updateFormData}
              handleSubmit={handleFormSubmit}
              handleBack={handleBack}
              uploading={uploading}
            />
          )}
        </div>
      </div>

      <RegistrationSuccess
        open={showSuccessDialog}
        onClose={handleCloseSuccessDialog}
      />
    </div>
  );
};

export default Onboarding;
