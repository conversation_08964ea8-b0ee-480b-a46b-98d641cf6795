import React from 'react';
import { MapPin, User, Navigation } from 'lucide-react';
import {
  useGeolocation,
  extractBookingCoordinates,
} from '@/hooks/useGeolocation';
import { Booking } from '@/store/api/customerApiSlice';

interface CustomerInfoProps {
  booking: Booking;
  currentServiceStatus: string;
  showDirections?: boolean;
}

const CustomerInfo: React.FC<CustomerInfoProps> = ({
  booking,
  currentServiceStatus,
  showDirections = true,
}) => {
  const { getDirections } = useGeolocation();

  const handleGetDirections = (e: React.MouseEvent) => {
    e.stopPropagation();

    const coordinates = extractBookingCoordinates(booking);
    if (coordinates) {
      getDirections(coordinates.lat, coordinates.lng);
    }
  };

  const shouldShowDirections =
    showDirections &&
    (booking.booking_status === 'Pending' ||
      booking.booking_status === 'Accepted') &&
    currentServiceStatus !== 'started' &&
    currentServiceStatus !== 'completed' &&
    currentServiceStatus !== 'payment_received';

  return (
    <div className='flex-1'>
      <div className='mb-2'>
        <div className='text-sm text-gray-800 mb-2'>Patient</div>
        <div className='flex items-center gap-3'>
          <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
            <User className='w-6 h-6 text-white' />
          </div>
          <div className='font-semibold text-gray-900 text-lg'>
            {booking.customer_given_name}
          </div>
        </div>
      </div>

      <div className='flex items-start gap-2 mb-2'>
        <MapPin className='w-5 h-5 text-nursery-darkBlue mt-0.5 flex-shrink-0' />
        <div className='flex-1 text-sm text-gray-800'>
          {booking.customer_booked_location_address}
        </div>
      </div>

      {shouldShowDirections && (
        <div className='mb-3 ml-7'>
          <button
            onClick={handleGetDirections}
            className='flex items-center gap-1 px-3 py-2 text-[#7039D6] bg-[#7039D62E] rounded-lg hover:bg-opacity-85 transition-colors text-sm font-medium'
            title='Get directions from your current location to customer location'
          >
            <Navigation className='w-4 h-4' />
            Get Directions
          </button>
        </div>
      )}
    </div>
  );
};

export default CustomerInfo;
