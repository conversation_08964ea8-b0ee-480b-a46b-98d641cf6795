# CometChat Integration Setup Guide

This guide explains how to set up and use the CometChat integration in your nurse service application.

## Overview

The chat functionality has been integrated using CometChat's real-time messaging service. The implementation includes:

- **Comet<PERSON>hatProvider**: Initializes CometChat when the app starts
- **ChatInterface**: A modal component that handles the chat UI and messaging
- **Chat Page**: Updated to show patient list and open chat windows

## Setup Instructions

### 1. CometChat Credentials

The CometChat credentials are already configured in `src/lib/cometchat.ts`:

```typescript
const COMETCHAT_CONSTANTS = {
  APP_ID: '27867821474b5952',
  REGION: 'IN',
  AUTH_KEY: '9a51b8294305015bff760513e3fea42187e9edbd',
};
```

### 2. Dependencies Installed

The following packages have been installed:

- `@cometchat/chat-uikit-react`: CometChat UI Kit for React
- `@cometchat-pro/chat`: CometChat SDK

### 3. Components Added

#### CometChatProvider (`src/components/CometChatProvider.tsx`)

- Initializes CometChat when the app starts
- Shows loading screen during initialization
- Wraps the entire application

#### ChatInterface (`src/components/ChatInterface.tsx`)

- Modal chat window component
- Handles real-time messaging
- Manages user authentication
- Loads and displays message history

#### Updated Chat Page (`src/pages/Chat.tsx`)

- Shows list of patients from bookings
- Clicking a patient opens the chat interface
- Integrated with existing booking data

### 4. Configuration Files

#### CometChat Configuration (`src/lib/cometchat.ts`)

Contains utility functions for:

- Initializing CometChat
- User login/logout
- User creation
- Message handling

## How It Works

### 1. Initialization

When the app starts, `CometChatProvider` initializes CometChat with your credentials.

### 2. User Authentication

When a user opens the chat:

- The system checks if the current nurse user exists in CometChat
- If not, it creates a new user account
- Logs in the user automatically

### 3. Patient Setup

When a nurse selects a patient to chat with:

- The system creates a CometChat user for the patient (if they don't exist)
- Uses the patient's `customer_cognitoId` as their CometChat UID
- Uses the patient's `customer_given_name` as their display name

### 4. Messaging

- Real-time messaging between nurse and patient
- Message history is loaded when chat opens
- Messages are displayed in a chat bubble interface
- Support for Enter key to send messages

## Usage

### For Nurses

1. Navigate to the Chat page
2. See list of patients from accepted/completed bookings
3. Click on a patient to open chat
4. Send and receive messages in real-time

### For Patients

Patients will need to be set up with CometChat accounts to receive messages. This can be done by:

- Creating patient accounts through the CometChat dashboard
- Using the CometChat API to create users programmatically
- Having patients register through your app

## Features

- ✅ Real-time messaging
- ✅ Message history
- ✅ User authentication
- ✅ Patient list integration
- ✅ Responsive design
- ✅ Loading states
- ✅ Error handling

## Security Notes

- The current implementation uses Auth Key for simplicity
- For production, consider using Auth Tokens for better security
- Patient data is handled through your existing booking system
- Messages are stored and managed by CometChat

## Troubleshooting

### Common Issues

1. **Chat not initializing**
   - Check CometChat credentials in `src/lib/cometchat.ts`
   - Verify internet connection
   - Check browser console for errors

2. **Messages not sending**
   - Ensure both users exist in CometChat
   - Check if users are properly authenticated
   - Verify CometChat service status

3. **Patient not appearing**
   - Check if patient has accepted/completed bookings
   - Verify patient data in localStorage
   - Ensure patient user is created in CometChat

### Debug Mode

To enable debug logging, add this to your browser console:

```javascript
localStorage.setItem('cometchat_debug', 'true');
```

## Next Steps

1. **Test the integration** with your existing patient data
2. **Set up patient accounts** in CometChat dashboard
3. **Customize the UI** to match your app's design
4. **Add additional features** like file sharing, voice messages, etc.
5. **Implement push notifications** for new messages

## Support

For CometChat-specific issues, refer to:

- [CometChat Documentation](https://www.cometchat.com/docs/ui-kit/react/react-js-integration)
- [CometChat Support](https://www.cometchat.com/support)

For application-specific issues, check the browser console and network tab for error details.
