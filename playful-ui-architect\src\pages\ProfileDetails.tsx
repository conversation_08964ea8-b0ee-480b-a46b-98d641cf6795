import React, { useEffect, useState } from 'react';
import ProfileHeader from '@/components/profile/ProfileHeader';
import PersonalDetailsSection from '@/components/profile/PersonalDetailsSection';
import ProfessionalDetailsSection from '@/components/profile/ProfessionalDetailsSection';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { toast } from '@/utils/toast';

type RTKError = {
  status: number | string;
  data?: { error?: string; message?: string };
};

const isRTKError = (err: unknown): err is RTKError => {
  return typeof err === 'object' && err !== null && 'status' in err;
};

const handleProfileDetailsError = (error: unknown) => {
  console.error('Profile details fetch error:', error);

  if (isRTKError(error)) {
    const errorMessage = error.data?.error || error.data?.message;
    const statusCode = error.status;

    switch (statusCode) {
      case 401:
        toast.error('Session expired. Please log in again.');
        break;
      case 403:
        toast.error('Access denied. Please check your permissions.');
        break;
      case 404:
        toast.error('Profile details not found. Please try refreshing.');
        break;
      case 500:
        toast.error('Server error. Please try again later.');
        break;
      default:
        toast.error(
          errorMessage || 'Failed to load profile details. Please try again.'
        );
    }
  } else {
    if (!navigator.onLine) {
      toast.error('Network error. Please check your connection.');
    } else {
      toast.error('Failed to load profile details. Please try again.');
    }
  }
};

const getProfileStringValue = (
  data: unknown,
  path: string,
  defaultValue: string = ''
): string => {
  if (typeof data === 'object' && data !== null && 'details' in data) {
    const details = (data as { details: Record<string, unknown> }).details;
    const value = details?.[path];
    return typeof value === 'string' ? value : defaultValue;
  }
  return defaultValue;
};

const getProfileNumberValue = (
  data: unknown,
  path: string,
  defaultValue?: number
): number | undefined => {
  if (typeof data === 'object' && data !== null && 'details' in data) {
    const details = (data as { details: Record<string, unknown> }).details;
    const value = details?.[path];
    return typeof value === 'number' ? value : defaultValue;
  }
  return defaultValue;
};

const getProfileStringArrayValue = (
  data: unknown,
  path: string,
  defaultValue: string[] = []
): string[] => {
  if (typeof data === 'object' && data !== null && 'details' in data) {
    const details = (data as { details: Record<string, unknown> }).details;
    const value = details?.[path];
    if (Array.isArray(value)) {
      return value.filter((item): item is string => typeof item === 'string');
    }
  }
  return defaultValue;
};

const getProfileDate = (data: unknown, path: string): Date => {
  if (typeof data === 'object' && data !== null && 'details' in data) {
    const details = (data as { details: Record<string, unknown> }).details;
    const dateValue = details?.[path];
    return dateValue ? new Date(dateValue as string) : new Date();
  }
  return new Date();
};

const ProfileDetails = () => {
  const [personalExpanded, setPersonalExpanded] = useState(true);
  const [professionalExpanded, setProfessionalExpanded] = useState(true);
  const [dob, setDob] = useState<Date>(new Date());
  const [gender, setGender] = useState<string>('');
  const [about, setAbout] = useState<string>('');
  const [given_name, setGiven_name] = useState<string>('');
  const [family_name, setFamily_name] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [emergencyContact, setEmergencyContact] = useState<string>('');
  const [servicesProvided, setServicesProvided] = useState<string[]>([]);
  const [totalExperience, setTotalExperience] = useState<number>();
  const { data, isLoading, error } = useGetProfileDetailsQuery();

  useEffect(() => {
    if (data) {
      setGiven_name(getProfileStringValue(data, 'given_name'));
      setFamily_name(getProfileStringValue(data, 'family_name'));
      setDob(getProfileDate(data, 'date_of_birth'));
      setGender(getProfileStringValue(data, 'gender'));
      setEmail(getProfileStringValue(data, 'email'));
      setEmergencyContact(getProfileStringValue(data, 'emergency_contact'));
      setTotalExperience(getProfileNumberValue(data, 'total_years_of_exp'));
      setServicesProvided(getProfileStringArrayValue(data, 'service_provide'));
    }
  }, [data]);

  useEffect(() => {
    if (error) {
      handleProfileDetailsError(error);
    }
  }, [error]);

  if (isLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  return (
    <div className='min-h-screen flex flex-col bg-white justify-center '>
      <ProfileHeader title='Profile Details' />

      <main className='flex-1 px-4 pb-8 w-11/12 mx-auto md:w-9/12 transition-all duration-200 ease-linear '>
        <div className='-mt-12'>
          <PersonalDetailsSection
            expanded={personalExpanded}
            setExpanded={setPersonalExpanded}
            given_name={given_name}
            setGiven_name={setGiven_name}
            family_name={family_name}
            setFamily_name={setFamily_name}
            dob={dob}
            setDob={setDob}
            gender={gender}
            setGender={setGender}
            email={email}
            setEmail={setEmail}
            isDisabled={true}
          />

          <ProfessionalDetailsSection
            expanded={professionalExpanded}
            setExpanded={setProfessionalExpanded}
            about={about}
            setAbout={setAbout}
            totalExperience={totalExperience}
            setTotalExperience={setTotalExperience}
            emergencyContact={emergencyContact}
            setEmergencyContact={setEmergencyContact}
            servicesProvided={servicesProvided}
            setServicesProvided={setServicesProvided}
          />
        </div>
      </main>
    </div>
  );
};

export default ProfileDetails;
