import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Logo from '../components/onBoarding/Logo/Logo';
import { usePhoneLoginMutation } from '@/store/api/apiSlice';
import { toast } from '@/utils/toast';
import { Eye, EyeOff } from 'lucide-react';

const Login = () => {
  const navigate = useNavigate();
  const [phone_number, setPhone] = useState('');
  const [phoneLogin, { isLoading }] = usePhoneLoginMutation();
  const [password, setPassword] = useState('');
  const [phoneNumberError, setPhoneNumberError] = useState<string>('');
  const [passwordError, setPasswordError] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let hasErrors = false;

    const numbersOnly = phone_number.replace('+91', '');
    if (!/^[0-9]{10}$/.test(numbersOnly)) {
      setPhoneNumberError('Please Enter a Valid 10-digit Mobile number.');
      hasErrors = true;
    } else {
      setPhoneNumberError('');
    }

    if (!password || !password.trim()) {
      setPasswordError('Password is required.');
      hasErrors = true;
    } else {
      setPasswordError('');
    }

    if (hasErrors) {
      return;
    }

    try {
      localStorage.clear();
      sessionStorage.clear();

      const response = await phoneLogin({ phone_number, password }).unwrap();
      if (response) {
        if (response?.tokens?.accessToken) {
          localStorage.setItem('token', response.tokens.accessToken);
        }
        if (response?.tokens?.idToken) {
          localStorage.setItem('idToken', response.tokens.idToken);
        }
        if (response?.tokens?.refreshToken) {
          localStorage.setItem('refreshToken', response.tokens.refreshToken);
        }

        toast.success('Login successful');
        navigate('/home', {
          state: {
            given_name: response.user?.given_name,
            username: response.user?.username,
            address: response.user?.address,
            nurse_onboarding_completed: response.user?.nurse_onboard_complete,
          },
        });
      }
    } catch (error: unknown) {
      console.error('Login error:', error);

      const isRTKError = (
        err: unknown
      ): err is {
        status: number | string;
        data?: { error?: string; message?: string };
      } => {
        return typeof err === 'object' && err !== null && 'status' in err;
      };

      if (
        isRTKError(error) &&
        (error.status === 404 || error.data?.error === 'User not found')
      ) {
        toast.error(
          'User not found. Please check your phone number or register first.'
        );
      } else if (
        isRTKError(error) &&
        (error.status === 401 ||
          error.data?.error === 'Incorrect username or password')
      ) {
        toast.error('Incorrect phone number or password. Please try again.');
      } else if (
        isRTKError(error) &&
        (error.status === 403 || error.data?.error === 'User is not confirmed')
      ) {
        toast.error('Account not verified. Please verify your OTP first.');
      } else if (
        isRTKError(error) &&
        (error.status === 400 || error.data?.error === 'Invalid parameters')
      ) {
        toast.error('Invalid login details. Please check your inputs.');
      } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
        toast.error(
          'Network error. Please check your connection and try again.'
        );
      } else if (isRTKError(error) && error.status === 429) {
        toast.error('Too many login attempts. Please try again later.');
      } else if (isRTKError(error) && error.status === 500) {
        toast.error('Server error. Please try again later.');
      } else if (isRTKError(error) && error.data?.message) {
        toast.error(error.data.message);
      } else {
        toast.error('Login failed. Please try again.');
      }
    }
  };

  const handlePhoneNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    if (!value.startsWith('+91')) {
      value = '+91' + value.replace(/^\+91/, '');
    }
    setPhone(value);
  };

  const handlePassword = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setPassword(password);

    if (passwordError) {
      setPasswordError('');
    }

    if (password && password.length < 6) {
      setPasswordError('Password is Incorrect.');
    }
  };

  return (
    <div className='relative w-full overflow-hidden min-h-screen flex flex-col'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

      <div className='relative z-10 h-full w-full flex-1 flex flex-col items-center justify-center pt-10 px-6 gap-3'>
        <Logo />

        {}
        <div className='w-full max-w-md bg-white rounded-xl p-6 shadow-lg'>
          <h2 className='text-2xl font-bold mb-6 text-nursery-blue'>Sign In</h2>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <Input
              type='text'
              placeholder='Enter Mobile Number'
              value={phone_number}
              onChange={handlePhoneNumber}
              className='w-full h-11 text-base'
            />
            {phoneNumberError && (
              <p className='text-sm text-red-500'>{phoneNumberError}</p>
            )}

            <div className='relative'>
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter Password'
                value={password}
                onChange={handlePassword}
                className='w-full h-11 text-base'
              />
              <Button
                type='button'
                onClick={() => {
                  setShowPassword(!showPassword);
                }}
                className='bg-transparent absolute right-3 top-1 text-gray-500 hover:bg-transparent hover:text-gray-900 '
              >
                {showPassword ? <Eye size={20} /> : <EyeOff size={20} />}
              </Button>

              {passwordError && (
                <p className='text-sm text-red-500 mt-3'>{passwordError}</p>
              )}
            </div>

            <div className='flex justify-end'>
              <button
                type='button'
                className='text-nursery-blue hover:text-nursery-darkBlue text-base'
                onClick={() => navigate('/forgotpassword')}
              >
                Forgot Password?
              </button>
            </div>

            <Button
              type='submit'
              className='w-full h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg font-medium'
              disabled={isLoading}
            >
              Sign In
            </Button>
          </form>

          <div className='mt-6 text-center'>
            <p className='text-base'>
              Don&apos;t have an account?{' '}
              <button
                onClick={() => navigate('/signup')}
                className='text-nursery-blue hover:text-nursery-darkBlue font-medium'
              >
                Register as Nurse
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
