import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/utils/toast';
import { useVerifyOTPMutation } from '@/store/api/apiSlice';
import { useResendOTPMutation } from '@/store/api/apiSlice';

const otpSchema = z.object({
  confirmationCode: z
    .string()
    .length(6, 'OTP must be 6 digits')
    .regex(/^\d+$/, 'OTP must contain only numbers'),
});

type RTKError = {
  status: number | string;
  data?: { error?: string; message?: string };
};

const isRTKError = (err: unknown): err is RTKError => {
  return typeof err === 'object' && err !== null && 'status' in err;
};

const handleVerificationError = (error: unknown) => {
  console.error('OTP verification error:', error);

  if (isRTKError(error) && error.data?.error === 'Invalid confirmation code') {
    toast.error('Invalid OTP. Please check and try again.');
  } else if (
    isRTKError(error) &&
    error.data?.error === 'Confirmation code has expired'
  ) {
    toast.error('OTP has expired. Please request a new one.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error ===
      'Missing required fields. Username and confirmation code are required.'
  ) {
    toast.error('Missing required information. Please try again.');
  } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
    toast.error('Network error. Please check your connection and try again.');
  } else if (
    isRTKError(error) &&
    (error.status === 500 ||
      error.data?.error === 'Server error. Please try again later.')
  ) {
    toast.error('Server error. Please try again later.');
  } else if (isRTKError(error) && error.data?.message) {
    toast.error(error.data.message);
  } else {
    toast.error('OTP verification failed. Please try again.');
  }
};

const handleResendError = (error: unknown) => {
  console.error('OTP resend error:', error);

  if (
    isRTKError(error) &&
    (error.status === 404 || error.data?.error === 'User not found')
  ) {
    toast.error('User not found. Please check your phone number.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Phone number is required'
  ) {
    toast.error('Phone number is required.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Invalid phone number format'
  ) {
    toast.error('Invalid phone number format. Please check and try again.');
  } else if (
    isRTKError(error) &&
    (error.status === 429 ||
      error.data?.error === 'Too many attempts. Please try again later')
  ) {
    toast.error(
      'Too many attempts. Please wait before requesting another OTP.'
    );
  } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
    toast.error('Network error. Please check your connection and try again.');
  } else if (
    isRTKError(error) &&
    (error.status === 500 || error.data?.error === 'Failed to resend OTP')
  ) {
    toast.error('Failed to resend OTP. Please try again later.');
  } else if (isRTKError(error) && error.data?.message) {
    toast.error(error.data.message);
  } else {
    toast.error('Failed to resend OTP. Please try again.');
  }
};

const OTPVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [verifyOTP, { isLoading }] = useVerifyOTPMutation();
  const [resendOtp, { isLoading: isResending }] = useResendOTPMutation();
  const [confirmationCode, setOtp] = useState('');
  const [timer, setTimer] = useState(30);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const username = location.state?.username;
  const phone_number = location.state?.phone_number;

  useEffect(() => {
    if (username) {
      startTimer();
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [username]);

  if (!username) {
    navigate('/signup');
    return null;
  }

  const startTimer = () => {
    setTimer(30);
    if (timerRef.current) clearInterval(timerRef.current);

    timerRef.current = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prevTimer - 1;
      });
    }, 1000);
  };

  const handleChange = (value: string) => {
    setErrors(prev => ({ ...prev, otp: '' }));
    setOtp(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationResult = otpSchema.safeParse({ confirmationCode });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });

      setErrors(fieldErrors);
      return;
    }

    try {
      const result = await verifyOTP({ username, confirmationCode }).unwrap();

      if (result.message === 'User confirmed successfully') {
        toast.success('OTP verified successfully!');
        navigate('/login');
      }
    } catch (error: unknown) {
      handleVerificationError(error);
    }
  };

  const handleResend = async (e: React.FormEvent) => {
    e.preventDefault();
    if (timer > 0) return;

    try {
      const response = await resendOtp({ phone_number }).unwrap();
      if (response) {
        setOtp('');
        setErrors({});
        toast.success('New OTP has been sent to your mobile number');
        startTimer();
      }
    } catch (error: unknown) {
      handleResendError(error);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className='relative h-screen w-full overflow-hidden'>
      {}

      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-30' />
      <div className=' relative z-20 flex-1 flex flex-col items-center justify-center px-5 md:pt-32 pt-20'>
        <img
          src='/Images/Logo.svg'
          alt='Nurses team'
          className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mx-auto mb-4 '
        />

        <Card className='w-full max-w-md bg-white rounded-md p-6 shadow-lg'>
          <h2 className='text-2xl font-bold text-center mb-6 text-nursery-blue'>
            Verify OTP
          </h2>
          <p className='text-center text-gray-800 mb-6'>
            Please enter the 6-digit OTP sent to your Phone Number
          </p>

          <form onSubmit={handleSubmit} noValidate className='space-y-4'>
            <div>
              <Input
                id='otp'
                type='text'
                placeholder='Enter 6-digit OTP'
                value={confirmationCode}
                onChange={e => handleChange(e.target.value)}
                className='w-full h-11 text-center text-base tracking-widest transition-all'
                maxLength={6}
              />
              {errors.confirmationCode && (
                <p className='text-red-500 text-sm text-center'>
                  {errors.confirmationCode}
                </p>
              )}
            </div>

            <Button
              type='submit'
              className='w-full h-10 bg-[#4AB4CE] text-white'
              disabled={
                isLoading || isResending || confirmationCode.length !== 6
              }
            >
              {isLoading ? 'Verifying...' : 'Verify OTP'}
            </Button>

            <Button
              type='button'
              variant='outline'
              onClick={() => navigate('/signup')}
              className='w-full h-10 border-[#4AB4CE] text-[#4AB4CE]'
            >
              Back to Sign Up
            </Button>
          </form>
          <div className='mt-6 text-center'>
            <button
              onClick={handleResend}
              disabled={timer > 0}
              className={`text-base ${
                timer > 0 ? 'text-gray-500' : 'text-nursery-blue font-medium'
              }`}
            >
              {timer > 0
                ? `Resend verification code after ${formatTime(timer)}`
                : 'Resend verification code'}
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OTPVerification;
