import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Copy,
  Gift,
  TrendingUp,
  Star,
  Award,
  RefreshCw,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/utils/toast';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';

const ReferNurse = () => {
  const navigate = useNavigate();
  const { data: profileDetails } = useGetProfileDetailsQuery();
  const [referralLink, setReferralLink] = useState('');
  const [currentReferralCode, setCurrentReferralCode] = useState('');
  const NurseFullName =
    profileDetails?.details?.given_name && profileDetails?.details?.family_name
      ? `${profileDetails.details.given_name} ${profileDetails.details.family_name}`
      : 'Loading...';

  useEffect(() => {
    if (profileDetails?.details?.user_id) {
      const baseUrl = 'https://testnurservapp.nurserv.com/signup';
      const uniqueReferralId = crypto.randomUUID().substring(0, 8);
      const nurseId = profileDetails.details.user_id;
      const link = `${baseUrl}?ref=${uniqueReferralId}&nurse=${nurseId}`;
      setReferralLink(link);
      setCurrentReferralCode(uniqueReferralId);

      const referralSessions = JSON.parse(
        localStorage.getItem('referralSessions') || '{}'
      );
      referralSessions[uniqueReferralId] = {
        nurseId,
        nurseName: NurseFullName,
        createdAt: new Date().toISOString(),
        status: 'pending',
      };
      localStorage.setItem(
        'referralSessions',
        JSON.stringify(referralSessions)
      );
    }
  }, [profileDetails, NurseFullName]);

  const generateNewReferralLink = () => {
    if (profileDetails?.details?.user_id) {
      const baseUrl = 'https://testnurservapp.nurserv.com/';
      const uniqueReferralId = crypto.randomUUID().substring(0, 8);
      const nurseId = profileDetails.details.user_id;
      const link = `${baseUrl}?ref=${uniqueReferralId}&nurse=${nurseId}`;
      setReferralLink(link);
      setCurrentReferralCode(uniqueReferralId);

      const referralSessions = JSON.parse(
        localStorage.getItem('referralSessions') || '{}'
      );
      referralSessions[uniqueReferralId] = {
        nurseId,
        nurseName: profileDetails.details.given_name,
        createdAt: new Date().toISOString(),
        status: 'pending',
      };
      localStorage.setItem(
        'referralSessions',
        JSON.stringify(referralSessions)
      );

      toast.success('New referral link generated!');
    }
  };

  const copyToClipboard = () => {
    if (referralLink) {
      navigator.clipboard.writeText(referralLink);
      toast.success('Referral link copied to clipboard');
    }
  };

  const bonusPoints = [
    {
      icon: <TrendingUp className='h-5 w-5 text-nursery-blue' />,
      title: 'More Referrals, Less Platform Fee',
      description: 'Reduce your platform fees with each successful referral',
    },
    {
      icon: <Gift className='h-5 w-5 text-nursery-blue' />,
      title: 'Fare Hike Opportunities',
      description: 'Increase your earning potential with more referrals',
    },
    {
      icon: <Star className='h-5 w-5 text-nursery-blue' />,
      title: 'Rating-Based Rewards',
      description: 'Extra rewards when referred nurses get 4.5+ ratings',
    },
    {
      icon: <Award className='h-5 w-5 text-nursery-blue' />,
      title: 'Performance Bonuses',
      description: 'Additional incentives for high-quality referrals',
    },
  ];

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Refer A Nurse</h1>
        </div>
      </header>

      {}
      <main className='flex-1 flex items-center justify-center p-5'>
        <div className='max-w-6xl w-full mx-auto'>
          <div className='flex flex-col lg:flex-row gap-6'>
            <div className='flex-1 space-y-4 order-1 lg:order-1'>
              <h2 className='text-2xl font-bold text-nursery-darkBlue mt-2'>
                Referral Bonuses
              </h2>

              {bonusPoints.map(bonus => (
                <Card
                  key={bonus.title}
                  className=' bg-[#F2F2F2] border-l-4 border-l-nursery-blue shadow-md hover:shadow-xl hover:scale-105 transition-all duration-300 ease-in-out cursor-pointer hover:border-l-nursery-teal'
                >
                  <CardContent className='p-4'>
                    <div className='flex items-start space-x-3'>
                      <div className='flex-shrink-0 mt-1 transition-transform duration-300 ease-in-out hover:scale-105'>
                        {bonus.icon}
                      </div>
                      <div>
                        <h3 className='font-semibold text-gray-800 mb-1 transition-colors duration-300 ease-in-out hover:text-nursery-blue'>
                          {bonus.title}
                        </h3>
                        <p className='text-gray-600 text-sm transition-colors duration-300 ease-in-out hover:text-gray-700'>
                          {bonus.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {}
            <div className='flex-1 flex flex-col justify-start order-2 lg:order-2 '>
              <Card className='w-full shadow-md hover:shadow-xl transition-shadow duration-300 ease-in-out bg-[#F2F2F2]'>
                <CardHeader>
                  <CardTitle className='flex items-center space-x-2'>
                    <Gift className='h-6 w-6 text-nursery-blue' />
                    <span>Your Referral Link</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <Label>Nurse Name</Label>
                      <Input
                        value={NurseFullName || 'Loading...'}
                        disabled
                        className=''
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label>Current Referral Code</Label>
                      <div className='flex gap-2'>
                        <Input
                          value={currentReferralCode || 'Generating...'}
                          readOnly
                          disabled
                          className='flex-1'
                        />
                        <Button
                          variant='outline'
                          onClick={generateNewReferralLink}
                          disabled={!profileDetails?.details?.user_id}
                          size='icon'
                          className='bg-nursery-blue hover:bg-nursery-blue text-white hover:text-white transition-all duration-300 ease-in-out hover:scale-105'
                        >
                          <RefreshCw className='h-4 w-4 transition-transform duration-300 ease-in-out hover:transform-rotate-90' />
                        </Button>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>Referral Link</Label>
                      <div className='flex flex-col gap-2'>
                        <Input
                          value={referralLink || 'Generating link...'}
                          readOnly
                          className='w-full'
                        />
                        <div className='flex justify-end'>
                          <Button
                            variant='outline'
                            onClick={copyToClipboard}
                            disabled={!referralLink}
                            className='bg-nursery-blue hover:bg-nursery-blue text-white hover:text-white transition-all duration-300 ease-in-out'
                          >
                            <Copy className='h-4 w-4 transition-transform duration-300 ease-in-out ' />
                            Copy
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className='bg-blue-50 p-4 rounded-lg border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-all duration-300 ease-in-out hover:shadow-md'>
                      <p className='text-sm text-nursery-darkBlue'>
                        <strong>💡How it works:</strong> Share this link with
                        potential nurses. When they sign up using your referral
                        code and successfully complete their first service,
                        you’ll receive a bonus reward.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ReferNurse;
