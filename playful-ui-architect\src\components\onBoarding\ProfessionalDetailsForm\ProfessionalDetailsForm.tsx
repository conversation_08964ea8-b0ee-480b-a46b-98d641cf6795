import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useGetServiceProvidersQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { Textarea } from '@/components/ui/textarea';
import SessionExpiredError from '@/components/ui/SessionExpiredError';

interface ProfessionalDetailsFormProps {
  formData: {
    total_years_of_exp: number;
    selected_services: string[];
    service_provide: string[];
    custom_service: string;
    nuid: string;
    about: string;
  };
  updateFormData: (
    data: Partial<ProfessionalDetailsFormProps['formData']>
  ) => void;
  fieldErrors: Record<string, string>;
  handleNext: () => void;
  handleBack: () => void;
}

const ProfessionalDetailsForm = ({
  formData,
  updateFormData,
  fieldErrors,
  handleNext,
  handleBack,
}: ProfessionalDetailsFormProps) => {
  const [customService, setCustomService] = useState('');
  const [customServices, setCustomServices] = useState<string[]>(
    formData.custom_service ? [formData.custom_service] : []
  );
  const {
    data: serviceProviders,
    isLoading,
    error,
  } = useGetServiceProvidersQuery();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleNext();
  };

  const toggleService = (service: string) => {
    const newServices = formData.selected_services.includes(service)
      ? formData.selected_services.filter(s => s !== service)
      : [...formData.selected_services, service];

    updateFormData({
      selected_services: newServices,
      service_provide: newServices,
    });
  };

  const addCustomService = () => {
    if (
      customService.trim() !== '' &&
      !customServices.includes(customService)
    ) {
      const newCustomServices = [...customServices, customService];
      setCustomServices(newCustomServices);

      const newSelected = [...formData.selected_services, customService];
      updateFormData({
        selected_services: newSelected,
        custom_service: customService,
        service_provide: newSelected,
      });

      setCustomService('');
    }
  };

  const removeService = (service: string) => {
    setCustomServices(customServices.filter(s => s !== service));

    const newSelected = formData.selected_services.filter(s => s !== service);
    updateFormData({
      selected_services: newSelected,
      service_provide: newSelected,
    });
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center p-4'>
        <ResponsiveLoader />
      </div>
    );
  }

  if (error) {
    return <SessionExpiredError />;
  }

  return (
    <form onSubmit={handleSubmit} noValidate className='space-y-4'>
      <div>
        <label htmlFor='NUID_id' className='text-gray-800 text-sm'>
          Nurse Unique ID (NUID)*
        </label>
        <Input
          type='text'
          placeholder='NUID*'
          value={formData.nuid || ''}
          onChange={e => updateFormData({ nuid: e.target.value || '' })}
          className='w-full h-10 text-gray-700'
        />
        {fieldErrors?.nuid && (
          <p className='text-red-500 text-sm mt-2'>{fieldErrors.nuid}</p>
        )}
      </div>
      {}
      <div>
        <label htmlFor='total_years_of_exp' className='text-gray-800 text-sm'>
          Total Years of Experience (as a Nurse)*
        </label>
        <Input
          type='number'
          placeholder='in years'
          value={formData.total_years_of_exp || ''}
          onChange={e =>
            updateFormData({
              total_years_of_exp: parseInt(e.target.value) || 0,
            })
          }
          min='1'
          className='w-full h-10 text-gray-700'
        />
        {fieldErrors?.total_years_of_exp && (
          <p className='text-red-500 text-sm mt-2'>
            {fieldErrors.total_years_of_exp}
          </p>
        )}
      </div>

      {}
      <div className='space-y-2'>
        <p className='text-gray-700 font-medium'>
          What type of nursing services do you offer?
        </p>
        <p className='text-gray-500 text-sm'>You can change this later</p>

        <div className='flex flex-wrap gap-2 mt-2'>
          {serviceProviders?.map(service => (
            <Button
              key={service.id}
              variant={
                formData.selected_services.includes(service.service_name)
                  ? 'default'
                  : 'outline'
              }
              className={`rounded-full ${
                formData.selected_services.includes(service.service_name)
                  ? 'bg-[#4AB4CE] hover:bg-[#4AB4CE]'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
              onClick={() => toggleService(service.service_name)}
              type='button'
            >
              {service.service_name}
            </Button>
          ))}
        </div>

        {}
        {customServices.length > 0 && (
          <div className='flex flex-wrap gap-2 mt-2'>
            {customServices.map(service => (
              <Button
                key={service}
                variant='default'
                className='h-10 rounded-xl bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
                onClick={() => removeService(service)}
                type='button'
              >
                {service} ✕
              </Button>
            ))}
          </div>
        )}

        {fieldErrors?.selected_services && (
          <p className='text-red-500 text-sm mt-2'>
            {fieldErrors.selected_services}
          </p>
        )}
      </div>

      {}
      <div className='space-y-2'>
        <p className='text-gray-700 font-medium'>
          Do you provide any additional nursing services?
        </p>
        <div className='flex gap-2 justify-center items-center text-center'>
          <Input
            type='text'
            placeholder='Add Custom Service'
            value={customService}
            onChange={e => setCustomService(e.target.value)}
            className='w-full h-10 text-gray-700'
          />
          <Button
            type='button'
            className={`h-10 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white text-3xl text-center  ${customService.length < 4 ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={addCustomService}
            disabled={customService.length < 4}
          >
            +
          </Button>
        </div>
      </div>
      {fieldErrors?.service_provide && (
        <p className='text-red-500 text-sm mt-1'>
          {Array.isArray(fieldErrors.service_provide)
            ? fieldErrors.service_provide[0]
            : fieldErrors.service_provide}
        </p>
      )}
      <div>
        <label htmlFor='about' className='text-gray-800 text-sm'>
          Tell me about yourself*
        </label>
        <Textarea
          placeholder='your Professional summary*'
          value={formData.about || ''}
          onChange={e => updateFormData({ about: e.target.value || '' })}
          className='w-full h-10 text-gray-700'
        />
        {fieldErrors?.about && (
          <p className='text-red-500 text-sm mt-2'>{fieldErrors.about}</p>
        )}
      </div>

      {}
      <div className='pt-1'>
        <Button
          className='w-full h-10 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
          type='submit'
        >
          Save and Next
        </Button>

        <Button
          variant='outline'
          className='w-full h-10 mt-4 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
          onClick={handleBack}
          type='button'
        >
          Back
        </Button>
      </div>
    </form>
  );
};

export default ProfessionalDetailsForm;
