import React, { useEffect, useState } from 'react';
import { ChevronDown, ChevronUp, Calendar } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  useUpdateNursePersonalDetailsMutation,
  useGetProfileDetailsQuery,
} from '../../store/api/apiSlice';
import { toast } from 'sonner';

interface PersonalDetailsSectionProps {
  expanded: boolean;
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>;

  given_name: string;
  setGiven_name: React.Dispatch<React.SetStateAction<string>>;

  family_name: string;
  setFamily_name: React.Dispatch<React.SetStateAction<string>>;

  gender: string;
  setGender: React.Dispatch<React.SetStateAction<string>>;

  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;

  dob: Date;
  setDob: React.Dispatch<React.SetStateAction<Date>>;

  isDisabled?: boolean;
}

const getNestedValue = (
  obj: unknown,
  path: string,
  defaultValue: string = ''
): string => {
  if (typeof obj === 'object' && obj !== null && 'details' in obj) {
    const details = (obj as { details: Record<string, unknown> }).details;
    const value = details?.[path];
    return typeof value === 'string' ? value : defaultValue;
  }
  return defaultValue;
};

const PersonalDetailsSection = ({
  expanded,
  setExpanded,
  given_name,
  setGiven_name,
  family_name,
  setFamily_name,
  dob,
  setDob,
  gender,
  setGender,
  email,
  setEmail,
  isDisabled = false,
}: PersonalDetailsSectionProps) => {
  const { data: currentDetails, refetch } = useGetProfileDetailsQuery(
    undefined,
    {
      skip: false,
    }
  );

  const [_initialValues, setInitialValues] = useState({
    given_name: given_name || '',
    family_name: family_name || '',
    date_of_birth: dob || '',
    gender: gender || '',
    email: email || '',
  });

  const [updatePersonalDetails, { isError, error }] =
    useUpdateNursePersonalDetailsMutation();

  useEffect(() => {
    if (currentDetails?.details) {
      const details = currentDetails.details;

      setGiven_name(getNestedValue(currentDetails, 'given_name'));
      setFamily_name(getNestedValue(currentDetails, 'family_name'));
      setGender(getNestedValue(currentDetails, 'gender'));
      setEmail(getNestedValue(currentDetails, 'email'));

      if (details.date_of_birth) {
        const dateObj = new Date(details.date_of_birth);
        if (!isNaN(dateObj.getTime())) {
          setDob(dateObj);
        }
      }

      setInitialValues({
        given_name: getNestedValue(currentDetails, 'given_name'),
        family_name: getNestedValue(currentDetails, 'family_name'),
        date_of_birth: details.date_of_birth
          ? new Date(details.date_of_birth)
          : '',
        gender: getNestedValue(currentDetails, 'gender'),
        email: getNestedValue(currentDetails, 'email'),
      });
    }
  }, [
    currentDetails,
    setDob,
    setEmail,
    setFamily_name,
    setGender,
    setGiven_name,
  ]);

  useEffect(() => {
    if (isError) {
      toast.error('Failed to update details. Please try again.', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      console.error('Update Error details:', error);
    }
  }, [isError, error]);

  const handleUpdateProfile = async () => {
    try {
      const formattedDate =
        dob instanceof Date && !isNaN(dob.getTime())
          ? dob.toISOString().split('T')[0]
          : null;

      const personalDetails = {
        given_name,
        family_name,
        date_of_birth: formattedDate,
        gender,
        email,
      };

      const result = await updatePersonalDetails(personalDetails).unwrap();

      if (result?.message?.includes('updated successfully') || result) {
        toast.success('Personal Details Updated Successfully', {
          style: { backgroundColor: '#FFFFFF', color: '#16A34A' },
          duration: 2000,
          dismissible: true,
        });

        setInitialValues({
          given_name: given_name || '',
          family_name: family_name || '',
          date_of_birth: dob || '',
          gender: gender || '',
          email: email || '',
        });

        await refetch();
      } else {
        toast.error(result?.message || 'Update Failed. Please try again.', {
          style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
          duration: 3000,
          dismissible: true,
        });
      }
    } catch (err) {
      console.error('Failed to update profile:', err);
      toast.error(
        'Update failed. Please check your connection and try again.',
        {
          style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
          duration: 3000,
          dismissible: true,
        }
      );
    }
  };

  const lowerCasedGender = gender?.toLowerCase() ?? '';

  return (
    <div className='relative z-10 bg-[#F2F2F2] rounded-lg  p-5 mb-4 shadow-lg'>
      <Collapsible
        open={expanded}
        onOpenChange={setExpanded}
        className='w-full'
      >
        <CollapsibleTrigger asChild className='w-full'>
          <div className='flex justify-between items-center cursor-pointer py-2'>
            <h2 className='text-xl font-bold'>Personal Details</h2>
            {expanded ? (
              <ChevronUp className='h-6 w-6 text-gray-400' />
            ) : (
              <ChevronDown className='h-6 w-6 text-gray-400' />
            )}
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent className='space-y-4 mt-2'>
          <fieldset
            disabled={isDisabled}
            className={cn('space-y-4', {
              'opacity-95 pointer-events-none': isDisabled,
            })}
          >
            <div>
              <label className='text-gray-800 text-sm'>First Name *</label>
              <Input
                type='text'
                placeholder='Enter You First Name'
                className='mt-1'
                value={given_name}
                onChange={e => setGiven_name(e.target.value)}
              />
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Last Name *</label>
              <Input
                type='text'
                className='mt-1'
                value={family_name}
                placeholder='Enter Your Last Name'
                onChange={e => setFamily_name(e.target.value)}
              />
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Date of Birth *</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant='outline'
                    className='w-full justify-between text-left font-normal mt-1 h-10'
                  >
                    {}
                    {dob ? format(dob, 'yyyy-MM-dd') : <span>Select date</span>}
                    <Calendar className='h-4 w-4 opacity-50' />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-auto p-0' align='start'>
                  <CalendarComponent
                    mode='single'
                    selected={dob}
                    onSelect={date => date && setDob(date)}
                    initialFocus
                    className='p-3 pointer-events-auto'
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Gender *</label>
              <Select value={lowerCasedGender} onValueChange={setGender}>
                <SelectTrigger className='w-full mt-1'>
                  <SelectValue placeholder='Select gender' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='male'>Male</SelectItem>
                  <SelectItem value='female'>Female</SelectItem>
                  <SelectItem value='other'>Other</SelectItem>
                  <SelectItem value='prefer_not_to_say'>
                    Prefer not to say
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Enter Your Email</label>
              <Input
                type='email'
                placeholder='Enter Your Email here'
                value={email}
                className='mt-1'
                onChange={e => setEmail(e.target.value)}
              />
            </div>
            <Button
              className='bg-nursery-darkBlue hover:bg-[#1e6880] text-white hover:shadow-xl transition-colors duration-300 ease-in-out'
              onClick={handleUpdateProfile}
            >
              Update Profile
            </Button>
          </fieldset>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default PersonalDetailsSection;
